# Testing the Notification System

## Test Scenarios

### 1. Test User Signup Notification
**Endpoint:** `POST /api/auth/signup`
**Expected:** Automatic notification created with message "Welcome to FlowCus! Your account has been created successfully."

### 2. Test Get All Notifications
**Endpoint:** `GET /api/notifications`
**Headers:** `Authorization: Bearer <token>`
**Expected:** Returns all notifications for the user

### 3. Test Mark Notification as Read
**Endpoint:** `PATCH /api/notifications/mark-as-read`
**Body:** `{ "notificationId": "notification_id_here" }`
**Expected:** Notification marked as read

### 4. Test Task Completion Notification
**Steps:**
1. Create a task with todo items
2. Update task to mark all todo items as completed (100% completion)
**Expected:** Automatic notification created for task completion

### 5. Test Habit Completion Notification
**Steps:**
1. Create a habit
2. Update habit completion status to true for today
**Expected:** Automatic notification created for habit completion

### 6. Test User Follow Notification
**Steps:**
1. User A follows User B
**Expected:** User B receives notification that User A started following them

### 7. Test Password Change Notification
**Steps:**
1. Reset password using forgot password flow
**Expected:** Notification created about password change

### 8. Test Mark All as Read
**Endpoint:** `PATCH /api/notifications/mark-all-as-read`
**Expected:** All notifications marked as read

### 9. Test Delete Notification
**Endpoint:** `DELETE /api/notifications/:notificationId`
**Expected:** Specific notification deleted

### 10. Test Clear All Notifications
**Endpoint:** `DELETE /api/notifications`
**Expected:** All notifications cleared

## Sample Test Requests

### Create Test User and Get Notifications
```bash
# 1. Sign up (should create welcome notification)
curl -X POST http://localhost:5001/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
  }'

# 2. Get notifications (use token from signup response)
curl -X GET http://localhost:5001/api/notifications \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 3. Mark notification as read
curl -X PATCH http://localhost:5001/api/notifications/mark-as-read \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "notificationId": "NOTIFICATION_ID_HERE"
  }'
```

## Expected Results

### Signup Response with Notification
After signup, when you call GET /api/notifications, you should see:
```json
{
  "message": "Notifications retrieved successfully",
  "notifications": [
    {
      "notificationId": "...",
      "message": "Welcome to FlowCus! Your account has been created successfully.",
      "type": "signup",
      "isRead": false,
      "date": "2024-01-15T10:30:00.000Z",
      "time": "10:30 AM"
    }
  ],
  "totalCount": 1,
  "unreadCount": 1
}
```

## Verification Checklist
- [ ] Signup creates welcome notification
- [ ] Task completion (100%) creates notification
- [ ] Habit completion creates notification
- [ ] Following user creates notification for followed user
- [ ] Password reset creates notification
- [ ] Get all notifications works
- [ ] Mark as read works
- [ ] Mark all as read works
- [ ] Delete notification works
- [ ] Clear all notifications works
- [ ] Notifications are sorted by date (recent first)
- [ ] Unread count is accurate
- [ ] All endpoints require authentication
