# Testing the New Notification System

## Test Scenarios

### 1. Test User Signup Notification
**Endpoint:** `POST /api/auth/signup`
**Expected:**
- Automatic in-app notification created with text "Welcome to FlowCus! Your account has been created successfully."
- Push notification sent if user has valid device token and allowNotification is true

### 2. Test Get All Notifications
**Endpoint:** `GET /api/notifications`
**Headers:** `Authorization: Bearer <token>`
**Expected:** Returns all notifications for the user from separate collection

### 3. Test Task Completion Notification
**Steps:**
1. Create a task with todo items
2. Update task to mark all todo items as completed (100% completion)
**Expected:**
- Automatic in-app notification created for task completion
- Push notification sent if enabled

### 4. Test Habit Completion Notification
**Steps:**
1. Create a habit
2. Update habit completion status to true for today
**Expected:**
- Automatic in-app notification created for habit completion
- Push notification sent if enabled

### 5. Test User Follow Notification
**Steps:**
1. User A follows User B
**Expected:**
- User B receives in-app notification that User A started following them
- Push notification sent to User B if enabled

### 6. Test Password Change Notification
**Steps:**
1. Reset password using forgot password flow
**Expected:**
- In-app notification created about password change
- Push notification sent if enabled

### 7. Test Delete Notification
**Endpoint:** `DELETE /api/notifications/:notificationId`
**Expected:** Specific notification deleted from collection

### 8. Test Clear All Notifications
**Endpoint:** `DELETE /api/notifications`
**Expected:** All notifications cleared from collection

### 9. Test Push Notification Settings
**Steps:**
1. Update user's allowNotification to false
2. Trigger any notification event
**Expected:** In-app notification created but no push notification sent

### 10. Test Device Token Management
**Steps:**
1. Update user's deviceToken field
2. Trigger notification event
**Expected:** Push notification sent to new device token

## Sample Test Requests

### Create Test User and Get Notifications
```bash
# 1. Sign up (should create welcome notification)
curl -X POST http://localhost:5001/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
  }'

# 2. Get notifications (use token from signup response)
curl -X GET http://localhost:5001/api/notifications \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 3. Mark notification as read
curl -X PATCH http://localhost:5001/api/notifications/mark-as-read \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "notificationId": "NOTIFICATION_ID_HERE"
  }'
```

## Expected Results

### Signup Response with Notification
After signup, when you call GET /api/notifications, you should see:
```json
{
  "message": "Notifications retrieved successfully",
  "notifications": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
      "text": "Welcome to FlowCus! Your account has been created successfully.",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "totalCount": 1
}
```

## Firebase Push Notification Testing

### Setup Firebase Testing
1. Create a Firebase project and get service account JSON
2. Set FCM_SERVICE_KEY environment variable with the JSON string
3. Get a device token from your mobile app
4. Update user's deviceToken field in database

### Test Push Notifications
```bash
# Update user's device token and notification settings
curl -X PATCH http://localhost:5001/api/profile/update \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceToken": "your_firebase_device_token_here",
    "allowNotification": true
  }'
```

## Verification Checklist
- [ ] Signup creates welcome notification (in-app + push)
- [ ] Task completion (100%) creates notification (in-app + push)
- [ ] Habit completion creates notification (in-app + push)
- [ ] Following user creates notification for followed user (in-app + push)
- [ ] Password reset creates notification (in-app + push)
- [ ] Get all notifications works (from separate collection)
- [ ] Delete notification works (from collection)
- [ ] Clear all notifications works (from collection)
- [ ] Notifications are sorted by createdAt (recent first)
- [ ] All endpoints require authentication
- [ ] Push notifications respect allowNotification setting
- [ ] Push notifications require valid deviceToken
- [ ] Firebase initialization handles invalid service keys gracefully
