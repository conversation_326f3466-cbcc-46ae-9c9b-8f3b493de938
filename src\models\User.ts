import mongoose, { Schema } from 'mongoose';
import { IUser } from '../interfaces/index';
import { followerItemSchema, followingItemSchema } from './FollowFollower';
import { blockedItemSchema } from './Block';
import { notificationItemSchema } from './Notification';

const userSchema: Schema = new Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  otp: { type: String },
  otpCreatedAt: { type: Date },
  // Profile data
  username: { type: String },
  gender: { type: String },
  dob: { type: Date },
  about: { type: String },
  profilePhoto: {
      type: String,
      default:
        "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS2TgOv9CMmsUzYKCcLGWPvqcpUk6HXp2mnww&s",
    },
  // Habit data
  habitCompletionPercentage: {
    type: Number,
    default: 0
  },
  // Follow data
  followers: [followerItemSchema],
  following: [followingItemSchema],
  followersCount: {
    type: Number,
    default: 0
  },
  followingCount: {
    type: Number,
    default: 0
  },
  // Chat/Online status
  isOnline: {
    type: Boolean,
    default: false
  },
  // Block data
  blockedUsers: [blockedItemSchema],
  blockedUsersCount: {
    type: Number,
    default: 0
  },
  // Notification data
  notifications: [notificationItemSchema]
}, {
  timestamps: true
});

const User = mongoose.model<IUser>('User', userSchema);
export default User;
