import { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middlewares/auth';
import FeedService from '../models/feed.model';
import { IFeedResponse } from '../interfaces/feed.interface';

/**
 * Get personalized feed for the authenticated user
 * Shows posts from following and followers, sorted by creation date (newest first)
 * Filters posts older than 5 days
 * Supports pagination with 10 posts per page by default
 */

export const getUserFeed = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    // Extract pagination parameters from query
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 1;

    // Validate pagination parameters
    if (page < 1) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Page number must be greater than 0'
      });
      return;
    }

    if (limit < 1 || limit > 50) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Limit must be between 1 and 50'
      });
      return;
    }

    // Get feed data from service
    const { feedItems, pagination } = await FeedService.getUserFeed(userId, page, limit);

    const response: IFeedResponse = {
      success: true,
      message: feedItems.length > 0 
        ? `Retrieved ${feedItems.length} posts for your feed` 
        : 'No posts available in your feed',
      feed: feedItems,
      pagination
    };

    res.status(StatusCodes.OK).json(response);

  } catch (error) {
    console.error('Error in getUserFeed controller:', error);
    
    if (error instanceof Error && error.message === 'User not found') {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve feed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

/**
 * Get feed statistics for the authenticated user
 * Shows information about available posts, following/followers count
 */
export const getFeedStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    const stats = await FeedService.getFeedStats(userId);

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Feed statistics retrieved successfully',
      stats
    });

  } catch (error) {
    console.error('Error in getFeedStats controller:', error);
    
    if (error instanceof Error && error.message === 'User not found') {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve feed statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

/**
 * Refresh feed - same as getUserFeed but with explicit refresh message
 * Useful for pull-to-refresh functionality
 */
export const refreshFeed = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    // Always get first page with default limit for refresh
    const page = 1;
    const limit = parseInt(req.query.limit as string) || 1;

    if (limit < 1 || limit > 50) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Limit must be between 1 and 50'
      });
      return;
    }

    const { feedItems, pagination } = await FeedService.getUserFeed(userId, page, limit);

    const response: IFeedResponse = {
      success: true,
      message: feedItems.length > 0 
        ? `Feed refreshed with ${feedItems.length} posts` 
        : 'No new posts available',
      feed: feedItems,
      pagination
    };

    res.status(StatusCodes.OK).json(response);

  } catch (error) {
    console.error('Error in refreshFeed controller:', error);
    
    if (error instanceof Error && error.message === 'User not found') {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to refresh feed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
