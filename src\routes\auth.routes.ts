import { Router } from 'express';
import { validateSignupRequest } from '../middlewares/index';
import 
{
    signup,
    login,
    resetPassword,
} from '../controllers/auth.controller';
import { resetPasswordGenerateOTP } from '../controllers/helper/OTP';
import { verifyOtp } from '../controllers/helper/OTP';

const router = Router();

router.post('/signup', validateSignupRequest, signup);
router.post('/login', login);
router.post('/forgot-password', resetPasswordGenerateOTP);
router.post('/resend-otp', resetPasswordGenerateOTP);
router.post('/verify-otp', verifyOtp);
router.post('/reset-password', resetPassword);   

export default router;
