# Notification API Documentation

## Overview
The Notification API provides comprehensive in-app notification management for FlowCus users with Firebase push notification support. Notifications are automatically triggered for important events like signup, task completion, habit completion, user follows, password changes, and more. All endpoints require JWT authentication.

## Base URL
```
http://localhost:5001/api/notifications
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Data Models

### Notification Schema (Simplified)
```typescript
{
  _id: ObjectId (unique identifier),
  userId: ObjectId (ref: User, required),
  text: String (required),
  createdAt: Date (auto-generated),
  updatedAt: Date (auto-generated)
}
```

### User Push Notification Settings
```typescript
{
  allowNotification: <PERSON>olean (default: true),
  deviceToken: String (Firebase FCM token)
}
```

## Features
- **In-App Notifications**: Stored in separate collection for better performance
- **Push Notifications**: Firebase FCM integration for real-time alerts
- **Automatic Triggers**: Notifications created for important user events
- **User Preferences**: Users can enable/disable push notifications
- **Device Token Management**: Support for Firebase device tokens

## API Endpoints

### 1. Get All Notifications
Retrieve all notifications for the authenticated user, sorted by date (most recent first).

**Endpoint:** `GET /api/notifications`

**Response:**
```json
{
  "message": "Notifications retrieved successfully",
  "notifications": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
      "text": "Congratulations! You have completed the task: \"Morning Workout\".",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "totalCount": 15
}
```

### 2. Delete Notification
Delete a specific notification.

**Endpoint:** `DELETE /api/notifications/:notificationId`

**Response:**
```json
{
  "message": "Notification deleted successfully",
  "notification": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
    "text": "Congratulations! You have completed the task: \"Morning Workout\".",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### 3. Clear All Notifications
Delete all notifications for the authenticated user.

**Endpoint:** `DELETE /api/notifications`

**Response:**
```json
{
  "message": "All notifications cleared successfully. 15 notifications were deleted.",
  "notifications": [],
  "totalCount": 0
}
```

## Automatic Notification Triggers

### User Signup
- **Trigger**: When a user successfully creates an account
- **Text**: "Welcome to FlowCus! Your account has been created successfully."
- **Push Notification**: Sent if user has `allowNotification: true` and valid `deviceToken`

### Task Completion
- **Trigger**: When a task reaches 100% completion (all todo items completed)
- **Text**: "Congratulations! You have completed the task: \"{taskTitle}\"."
- **Push Notification**: Sent if user has notifications enabled

### Habit Completion
- **Trigger**: When a user marks a habit as completed for the day
- **Text**: "Great job! You have completed your habit: \"{habitTitle}\" for today."
- **Push Notification**: Sent if user has notifications enabled

### User Followed
- **Trigger**: When someone follows the user
- **Text**: "{followerUsername} started following you."
- **Push Notification**: Sent if user has notifications enabled

### Password Changed
- **Trigger**: When a user successfully resets their password
- **Text**: "Your password has been changed successfully. If this wasn't you, please contact support."
- **Push Notification**: Sent if user has notifications enabled

## Push Notification Setup

### Firebase Configuration
1. Set `FCM_SERVICE_KEY` environment variable with Firebase service account JSON
2. Users must provide their Firebase device token via `deviceToken` field
3. Users can enable/disable push notifications via `allowNotification` field

### Push Notification Flow
1. In-app notification is created in database
2. If user has `allowNotification: true` and valid `deviceToken`:
   - Firebase push notification is sent automatically
   - Notification includes title "FlowCus" and body with notification text

## Error Responses

### 400 Bad Request
```json
{
  "message": "Valid notification ID is required"
}
```

### 401 Unauthorized
```json
{
  "message": "User not authenticated"
}
```

### 404 Not Found
```json
{
  "message": "Notification not found"
}
```

### 500 Internal Server Error
```json
{
  "message": "Error retrieving notifications",
  "error": "Detailed error message"
}
```

## Usage Examples

### Frontend Integration
```javascript
// Get all notifications
const response = await fetch('/api/notifications', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// Mark notification as read
await fetch('/api/notifications/mark-as-read', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    notificationId: '64f8a1b2c3d4e5f6a7b8c9d0'
  })
});

// Clear all notifications
await fetch('/api/notifications', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

## Features
- **Automatic Triggers**: Notifications are automatically created for important user events
- **Read Status Management**: Track which notifications have been read
- **Bulk Operations**: Mark all as read or clear all notifications
- **Rich Data**: Notifications include related data for context
- **Sorted Display**: Notifications are sorted by date (most recent first)
- **Type Categorization**: Different notification types for better organization
- **Security**: All endpoints require JWT authentication
- **Error Handling**: Comprehensive error responses with detailed messages
