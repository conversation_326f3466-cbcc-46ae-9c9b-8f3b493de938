# Notification API Documentation

## Overview
The Notification API provides comprehensive in-app notification management for FlowCus users. Notifications are automatically triggered for important events like signup, task completion, habit completion, user follows, password changes, and more. All endpoints require JWT authentication.

## Base URL
```
http://localhost:5001/api/notifications
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Data Models

### Notification Schema
```typescript
{
  notificationId: ObjectId (unique identifier),
  message: String (required, max 500 chars),
  type: String (enum: 'signup', 'task_completed', 'habit_completed', 'task_ended', 'password_changed', 'user_followed', 'general'),
  isRead: Boolean (default: false),
  date: Date (default: current date),
  time: String (formatted time, e.g., "02:30 PM"),
  relatedData?: {
    taskId?: ObjectId (ref: Task),
    habitId?: ObjectId (ref: Habit),
    fromUserId?: ObjectId (ref: User),
    fromUsername?: String
  }
}
```

### Notification Types
- **signup**: Welcome notification when user creates account
- **task_completed**: Notification when user completes a task (100% completion)
- **habit_completed**: Notification when user completes a habit for the day
- **task_ended**: Notification when a task's end time is reached
- **password_changed**: Security notification when password is reset
- **user_followed**: Social notification when someone follows the user
- **general**: Generic notifications for other events

## API Endpoints

### 1. Get All Notifications
Retrieve all notifications for the authenticated user, sorted by date (most recent first).

**Endpoint:** `GET /api/notifications`

**Response:**
```json
{
  "message": "Notifications retrieved successfully",
  "notifications": [
    {
      "notificationId": "64f8a1b2c3d4e5f6a7b8c9d0",
      "message": "Congratulations! You have completed the task: \"Morning Workout\".",
      "type": "task_completed",
      "isRead": false,
      "date": "2024-01-15T10:30:00.000Z",
      "time": "10:30 AM",
      "relatedData": {
        "taskId": "64f8a1b2c3d4e5f6a7b8c9d1"
      }
    }
  ],
  "totalCount": 15,
  "unreadCount": 3
}
```

### 2. Mark Notification as Read
Mark a specific notification as read.

**Endpoint:** `PATCH /api/notifications/mark-as-read`

**Request Body:**
```json
{
  "notificationId": "64f8a1b2c3d4e5f6a7b8c9d0"
}
```

**Response:**
```json
{
  "message": "Notification marked as read successfully",
  "notification": {
    "notificationId": "64f8a1b2c3d4e5f6a7b8c9d0",
    "message": "Congratulations! You have completed the task: \"Morning Workout\".",
    "type": "task_completed",
    "isRead": true,
    "date": "2024-01-15T10:30:00.000Z",
    "time": "10:30 AM"
  }
}
```

### 3. Mark All Notifications as Read
Mark all notifications as read for the authenticated user.

**Endpoint:** `PATCH /api/notifications/mark-all-as-read`

**Response:**
```json
{
  "message": "All notifications marked as read successfully",
  "notifications": [...],
  "totalCount": 15,
  "unreadCount": 0
}
```

### 4. Delete Notification
Delete a specific notification.

**Endpoint:** `DELETE /api/notifications/:notificationId`

**Response:**
```json
{
  "message": "Notification deleted successfully",
  "notification": {
    "notificationId": "64f8a1b2c3d4e5f6a7b8c9d0",
    "message": "Congratulations! You have completed the task: \"Morning Workout\".",
    "type": "task_completed",
    "isRead": false,
    "date": "2024-01-15T10:30:00.000Z",
    "time": "10:30 AM"
  }
}
```

### 5. Clear All Notifications
Delete all notifications for the authenticated user.

**Endpoint:** `DELETE /api/notifications`

**Response:**
```json
{
  "message": "All notifications cleared successfully. 15 notifications were deleted.",
  "notifications": [],
  "totalCount": 0,
  "unreadCount": 0
}
```

## Automatic Notification Triggers

### User Signup
- **Trigger**: When a user successfully creates an account
- **Message**: "Welcome to FlowCus! Your account has been created successfully."
- **Type**: `signup`

### Task Completion
- **Trigger**: When a task reaches 100% completion (all todo items completed)
- **Message**: "Congratulations! You have completed the task: \"{taskTitle}\"."
- **Type**: `task_completed`
- **Related Data**: `taskId`

### Habit Completion
- **Trigger**: When a user marks a habit as completed for the day
- **Message**: "Great job! You have completed your habit: \"{habitTitle}\" for today."
- **Type**: `habit_completed`
- **Related Data**: `habitId`

### User Followed
- **Trigger**: When someone follows the user
- **Message**: "{followerUsername} started following you."
- **Type**: `user_followed`
- **Related Data**: `fromUserId`, `fromUsername`

### Password Changed
- **Trigger**: When a user successfully resets their password
- **Message**: "Your password has been changed successfully. If this wasn't you, please contact support."
- **Type**: `password_changed`

## Error Responses

### 400 Bad Request
```json
{
  "message": "Valid notification ID is required"
}
```

### 401 Unauthorized
```json
{
  "message": "User not authenticated"
}
```

### 404 Not Found
```json
{
  "message": "Notification not found"
}
```

### 500 Internal Server Error
```json
{
  "message": "Error retrieving notifications",
  "error": "Detailed error message"
}
```

## Usage Examples

### Frontend Integration
```javascript
// Get all notifications
const response = await fetch('/api/notifications', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// Mark notification as read
await fetch('/api/notifications/mark-as-read', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    notificationId: '64f8a1b2c3d4e5f6a7b8c9d0'
  })
});

// Clear all notifications
await fetch('/api/notifications', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

## Features
- **Automatic Triggers**: Notifications are automatically created for important user events
- **Read Status Management**: Track which notifications have been read
- **Bulk Operations**: Mark all as read or clear all notifications
- **Rich Data**: Notifications include related data for context
- **Sorted Display**: Notifications are sorted by date (most recent first)
- **Type Categorization**: Different notification types for better organization
- **Security**: All endpoints require JWT authentication
- **Error Handling**: Comprehensive error responses with detailed messages
