import mongoose, { Schema } from 'mongoose';
import { IHabit } from '../interfaces/habits.interface';
import { getTodayDeviceDate, formatLocalDate, isSameLocalDay } from '../utils/dateUtils';

const todoListItemSchema: Schema = new Schema({
  date: { 
    type: Date, 
    required: true 
  },
  isCompleted: { 
    type: Boolean, 
    default: false 
  }
}, { _id: false });

const habitSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  title: { 
    type: String, 
    required: true,
    trim: true
  },
  icon: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String, 
    required: true,
    trim: true
  },
  todoList: [todoListItemSchema]
}, {
  timestamps: true
});

// Index for better query performance
habitSchema.index({ userId: 1 });
habitSchema.index({ userId: 1, title: 1 });

// Pre-save middleware to automatically add today's date to todoList if not exists
habitSchema.pre('save', function(next) {
  const habit = this as any;
  const today = getTodayDeviceDate();

  // Check if today's date already exists in todoList
  const todayExists = habit.todoList.some((item: any) => {
    return isSameLocalDay(new Date(item.date), today);
  });

  // If today's date doesn't exist, add it
  if (!todayExists) {
    habit.todoList.push({
      date: today,
      isCompleted: false
    });
    console.log(`Added today's date (${formatLocalDate(today)}) to habit: ${habit.title} during save`);
  } else {
    console.log(`Today's date (${formatLocalDate(today)}) already exists in habit: ${habit.title}`);
  }

  next();
});

// Static method to add daily entries for all habits using device date
habitSchema.statics.addDailyEntries = async function() {
  // Get today's date based on device time, not UTC
  const today = getTodayDeviceDate();

  const habits = await this.find({});
  let updatedCount = 0;
  let alreadyExistsCount = 0;

  for (const habit of habits) {
    const todayExists = habit.todoList.some((item: any) => {
      return isSameLocalDay(new Date(item.date), today);
    });

    if (!todayExists) {
      habit.todoList.push({
        date: today,
        isCompleted: false
      });
      await habit.save();
      updatedCount++;
      console.log(`Added today's date (${formatLocalDate(today)}) to habit: ${habit.title}`);
    } else {
      alreadyExistsCount++;
      console.log(`Today's date (${formatLocalDate(today)}) already exists in habit: ${habit.title}`);
    }
  }

  console.log(`Daily habit entries update completed for ${formatLocalDate(today)}:`);
  console.log(`- Updated habits: ${updatedCount}`);
  console.log(`- Habits already had today's date: ${alreadyExistsCount}`);
  console.log(`- Total habits processed: ${habits.length}`);
};

const Habit = mongoose.model<IHabit>('Habit', habitSchema);
export default Habit;
