import dotenv from 'dotenv';
import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import connectDB from './config/db';
import cors from "cors";
import bodyParser from "body-parser";
import helmet from "helmet";
import { rateLimiter } from "./utils/rateLimit";
import { StatusCodes } from "http-status-codes";
import {
  authRouter,
  profileRouter,
  taskRouter,
  habitRouter,
  followRouter,
  blockRouter,
  postRouter,
  communityRouter,
  adminRouter,
  chatRouter,
  feedRouter,
} from "./routes/index";
import { startHabitScheduler } from "./utils/habitScheduler";
import { initializeSocket } from "./socket/socketHandler";

// ----------- Config -------------------
dotenv.config();

// ----------- Server -------------------
const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: "*", // Configure this properly for production
    methods: ["GET", "POST"]
  }
});
const port = process.env.PORT || 5001;


// ---------- Middlewares ----------------------------
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
app.use(cors());
app.use(helmet());
app.use(rateLimiter()); 

// ----------- Routes -------------------
app.use('/api/auth', authRouter);
app.use('/api/admin', adminRouter);
app.use('/api/profile', profileRouter);
app.use('/api/tasks', taskRouter);        //home
app.use('/api/habits', habitRouter);
app.use('/api/follow', followRouter);
app.use('/api/block', blockRouter);
app.use('/api/posts', postRouter);
app.use('/api/communities', communityRouter);
app.use('/api/chat', chatRouter);
app.use('/api/feed', feedRouter);

// Connect to MongoDB
connectDB();

// Initialize Socket.IO
initializeSocket(io);

// Start habit scheduler
startHabitScheduler();

server.listen(port, () => {
  console.log('Server is running on port 5001');
  console.log('Socket.IO server initialized');
});
