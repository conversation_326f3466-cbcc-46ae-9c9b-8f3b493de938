import { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import mongoose from 'mongoose';
import User from '../models/User';
import { AuthenticatedRequest } from '../middlewares/auth';

// Block a user
export const blockUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userIdToBlock } = req.body;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate userIdToBlock
    if (!userIdToBlock || !mongoose.Types.ObjectId.isValid(userIdToBlock)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid user ID to block is required'
      });
      return;
    }

    // Check if trying to block themselves
    if (currentUserId === userIdToBlock) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Cannot block yourself'
      });
      return;
    }

    // Get both users
    const [currentUser, userToBlock] = await Promise.all([
      User.findById(currentUserId),
      User.findById(userIdToBlock)
    ]);

    if (!currentUser || !userToBlock) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Check if already blocked
    const isAlreadyBlocked = currentUser.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === userIdToBlock
    );

    if (isAlreadyBlocked) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'User is already blocked'
      });
      return;
    }

    // Remove any existing follow relationships
    // Remove from current user's following list
    const followingIndex = currentUser.following.findIndex(
      (followingItem) => followingItem.userId.toString() === userIdToBlock
    );
    if (followingIndex !== -1) {
      currentUser.following.splice(followingIndex, 1);
      currentUser.followingCount = Math.max(0, currentUser.followingCount - 1);
    }

    // Remove from current user's followers list
    const followerIndex = currentUser.followers.findIndex(
      (followerItem) => followerItem.userId.toString() === userIdToBlock
    );
    if (followerIndex !== -1) {
      currentUser.followers.splice(followerIndex, 1);
      currentUser.followersCount = Math.max(0, currentUser.followersCount - 1);
    }

    // Remove from blocked user's following list
    const blockedUserFollowingIndex = userToBlock.following.findIndex(
      (followingItem) => followingItem.userId.toString() === currentUserId
    );
    if (blockedUserFollowingIndex !== -1) {
      userToBlock.following.splice(blockedUserFollowingIndex, 1);
      userToBlock.followingCount = Math.max(0, userToBlock.followingCount - 1);
    }

    // Remove from blocked user's followers list
    const blockedUserFollowerIndex = userToBlock.followers.findIndex(
      (followerItem) => followerItem.userId.toString() === currentUserId
    );
    if (blockedUserFollowerIndex !== -1) {
      userToBlock.followers.splice(blockedUserFollowerIndex, 1);
      userToBlock.followersCount = Math.max(0, userToBlock.followersCount - 1);
    }

    // Add to current user's blocked list
    currentUser.blockedUsers.push({
      userId: userIdToBlock,
      isBlocked: true,
      blockedAt: new Date()
    });
    currentUser.blockedUsersCount += 1;

    // Save both users
    await Promise.all([
      currentUser.save(),
      userToBlock.save()
    ]);

    res.status(StatusCodes.OK).json({
      message: 'User blocked successfully',
      blockedUsersCount: currentUser.blockedUsersCount,
      blockedUser: {
        userId: userToBlock._id,
        username: userToBlock.username || ""
      }
    });
  } catch (error) {
    console.error('Error blocking user:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error blocking user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Unblock a user
export const unblockUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userIdToUnblock } = req.body;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate userIdToUnblock
    if (!userIdToUnblock || !mongoose.Types.ObjectId.isValid(userIdToUnblock)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid user ID to unblock is required'
      });
      return;
    }

    // Get current user
    const currentUser = await User.findById(currentUserId);
    if (!currentUser) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Check if user is actually blocked
    const blockedIndex = currentUser.blockedUsers.findIndex(
      (blockedItem) => blockedItem.userId.toString() === userIdToUnblock
    );

    if (blockedIndex === -1) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'User is not blocked'
      });
      return;
    }

    // Remove from blocked list
    currentUser.blockedUsers.splice(blockedIndex, 1);
    currentUser.blockedUsersCount = Math.max(0, currentUser.blockedUsersCount - 1);

    await currentUser.save();

    res.status(StatusCodes.OK).json({
      message: 'User unblocked successfully',
      blockedUsersCount: currentUser.blockedUsersCount
    });
  } catch (error) {
    console.error('Error unblocking user:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error unblocking user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get blocked users list
export const getBlockedUsers = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const currentUserId = req.user?.userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const currentUser = await User.findById(currentUserId)
      .populate({
        path: 'blockedUsers.userId',
        select: 'username profilePhoto email',
        options: {
          skip: (page - 1) * limit,
          limit: limit
        }
      });

    if (!currentUser) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    const blockedUsers = currentUser.blockedUsers
      .filter(item => item.isBlocked)
      .slice((page - 1) * limit, page * limit)
      .map(item => ({
        userId: item.userId,
        blockedAt: item.blockedAt || new Date()
      }));

    res.status(StatusCodes.OK).json({
      message: 'Blocked users retrieved successfully',
      blockedUsers,
      totalCount: currentUser.blockedUsersCount,
      currentPage: page,
      totalPages: Math.ceil(currentUser.blockedUsersCount / limit)
    });
  } catch (error) {
    console.error('Error getting blocked users:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error retrieving blocked users',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Check if a user is blocked or blocking
export const checkBlockStatus = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid user ID is required'
      });
      return;
    }

    const [currentUser, otherUser] = await Promise.all([
      User.findById(currentUserId).select('blockedUsers'),
      User.findById(userId).select('blockedUsers')
    ]);

    if (!currentUser || !otherUser) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    const isBlocked = currentUser.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === userId && blockedItem.isBlocked
    );

    const isBlockedBy = otherUser.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === currentUserId && blockedItem.isBlocked
    );

    res.status(StatusCodes.OK).json({
      message: 'Block status retrieved successfully',
      blockStatus: {
        isBlocked,
        isBlockedBy
      }
    });
  } catch (error) {
    console.error('Error checking block status:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error checking block status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
