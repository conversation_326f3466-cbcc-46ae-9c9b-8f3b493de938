import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import Chat from '../models/Chat';
import ChatMessage from '../models/ChatMessage';
import { IOnlineUser, ISendMessageRequest } from '../interfaces/index';

// Store online users
const onlineUsers = new Map<string, IOnlineUser>();

// JWT verification for socket connections
const verifySocketToken = async (token: string): Promise<{ userId: string; email: string } | null> => {
  try {
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      throw new Error('JWT_SECRET not defined');
    }

    const decoded = jwt.verify(token, JWT_SECRET) as {
      userId: string;
      email: string;
      iat: number;
      exp: number;
    };

    return { userId: decoded.userId, email: decoded.email };
  } catch (error) {
    console.error('Socket token verification failed:', error);
    return null;
  }
};

export const initializeSocket = (io: SocketIOServer) => {
  // Socket authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.headers.token;
      if (!token) {
        return next(new Error('Authentication token required'));
      }
     // console.log("Token in socket connection is = :", token);

      const user = await verifySocketToken(token as string);
      if (!user) {
        return next(new Error('Invalid authentication token'));
      }

      // Attach user info to socket
      socket.data.userId = user.userId;
      socket.data.email = user.email;
      //console.log("User ID in socket connection is = :", user.userId);

      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });

  io.on('connection', async (socket: Socket) => {
    const userId = socket.data.userId;
    const email = socket.data.email;

    console.log(`User ${userId} connected with socket ${socket.id}`);

    try {
      // Get user details
      const user = await User.findById(userId).select('username email profilePhoto');
      if (!user) {
        socket.disconnect();
        return;
      }

      // Update user online status
      await User.findByIdAndUpdate(userId, { isOnline: true });

      // Store online user
      const onlineUser: IOnlineUser = {
        userId,
        socketId: socket.id,
        username: user.username,
        email: user.email,
        profilePhoto: user.profilePhoto,
        connectedAt: new Date()
      };
      onlineUsers.set(userId, onlineUser);

      // Notify other users about online status
      socket.broadcast.emit('user-status-changed', {
        userId,
        isOnline: true
      });

      // Join user to their personal room
      socket.join(`user_${userId}`);
    //  console.log(`User ${userId} joined their personal room`);

      // Handle joining chat rooms
      socket.on('join-chat', async (chatId: string) => {
        try {
          // Verify user is participant in this chat
          console.log("Chat ID in join-chat is = :", chatId);
          const chat = await Chat.findOne({
            _id: chatId,
            participants: userId
          });

          if (!chat) {
            return;
          }

          // Get the other participant
          const otherParticipantId = chat.participants.find(id => id.toString() !== userId);
          if (!otherParticipantId) {
            return;
          }

          // Check for blocking relationships
          const [currentUser, otherUser] = await Promise.all([
            User.findById(userId).select('blockedUsers'),
            User.findById(otherParticipantId).select('blockedUsers')
          ]);

          if (!currentUser || !otherUser) {
            return;
          }

          // Check if current user has blocked the other participant
          const hasBlockedOther = currentUser.blockedUsers.some(
            (blockedItem) => blockedItem.userId.toString() === otherParticipantId.toString() && blockedItem.isBlocked
          );

          // Check if current user is blocked by the other participant
          const isBlockedByOther = otherUser.blockedUsers.some(
            (blockedItem) => blockedItem.userId.toString() === userId && blockedItem.isBlocked
          );

          // Only join if there's no blocking relationship
          if (!hasBlockedOther && !isBlockedByOther) {
            socket.join(`chat_${chatId}`);
            console.log(`User ${userId} joined chat ${chatId}`);
          }
        } catch (error) {
          console.error('Error joining chat due to blocking relationship:', error);
        }
      });

      // Handle leaving chat rooms
      socket.on('leave-chat', (chatId: string) => {
        socket.leave(`chat_${chatId}`);
        console.log(`User ${userId} left chat ${chatId}`);
      });

      // Handle sending messages
      socket.on('send-message', async (data: ISendMessageRequest) => {
        try {
          const { chatId, message, media, messageType } = data;

          // Verify user is participant in this chat
          const chat = await Chat.findOne({
            _id: chatId,
            participants: userId
          });

          if (!chat) {
            socket.emit('error', { message: 'Chat not found or access denied' });
            return;
          }

          // Get receiver ID
          const receiverId = chat.participants.find(id => id.toString() !== userId.toString());
          if (!receiverId) {
            socket.emit('error', { message: 'Receiver not found' });
            return;
          }

          // Check for blocking relationships
          const [senderUser, receiverUser] = await Promise.all([
            User.findById(userId).select('blockedUsers'),
            User.findById(receiverId).select('blockedUsers')
          ]);

          if (!senderUser || !receiverUser) {
            socket.emit('error', { message: 'User not found' });
            return;
          }

          // Check if sender has blocked the receiver
          const hasBlockedReceiver = senderUser.blockedUsers.some(
            (blockedItem) => blockedItem.userId.toString() === receiverId.toString() && blockedItem.isBlocked
          );

          // Check if sender is blocked by the receiver
          const isBlockedByReceiver = receiverUser.blockedUsers.some(
            (blockedItem) => blockedItem.userId.toString() === userId && blockedItem.isBlocked
          );

          if (hasBlockedReceiver || isBlockedByReceiver) {
            socket.emit('error', { message: 'Cannot send message due to blocking relationship' });
            return;
          }
          
          // Create new message
          const newMessage = new ChatMessage({
            chatId,
            senderId: userId,
            receiverId,
            message: message || '',
            media: media || [],
            messageType,
            isRead: false,
            sentAt: new Date()
          });

          await newMessage.save();

          // Update chat with last message info
          await Chat.findByIdAndUpdate(chatId, {
            lastMessage: message || (media && media.length > 0 ? 'Media message' : ''),
            lastMessageAt: new Date(),
            lastMessageSenderId: userId
          });

          // Populate sender info
          await newMessage.populate('senderId', 'username profilePhoto');
          console.log("Sender:", userId);
          console.log("Participants:", chat.participants);
          console.log("Resolved Receiver:", receiverId);

          io.to(`user_${receiverId}`).emit('message-received', newMessage);

          // Emit chat update to participants
          io.to(`user_${receiverId}`).emit('chat-updated', {
            chatId,
            lastMessage: message || 'Media message',
            lastMessageAt: new Date()
          });

        } catch (error) {
          console.error('Error sending message:', error);
          socket.emit('error', { message: 'Failed to send message' });
        }
      });

      // Handle marking messages as read
      socket.on('mark-as-read', async (data: { chatId: string; messageIds: string[] }) => {
        try {
          const { chatId, messageIds } = data;

          // Verify user is participant in this chat
          const chat = await Chat.findOne({
            _id: chatId,
            participants: userId
          });

          if (!chat) {
            socket.emit('error', { message: 'Chat not found or access denied' });
            return;
          }

          // Mark messages as read
          await ChatMessage.updateMany(
            {
              _id: { $in: messageIds },
              receiverId: userId,
              isRead: false
            },
            {
              isRead: true,
              readAt: new Date()
            }
          );

          // Notify sender about read status
          const senderId = chat.participants.find(id => id.toString() !== userId);
          if (senderId) {
            io.to(`user_${senderId}`).emit('message-read', {
              chatId,
              messageIds,
              readBy: userId
            });
          }

        } catch (error) {
          console.error('Error marking messages as read:', error);
          socket.emit('error', { message: 'Failed to mark messages as read' });
        }
      });

      // Handle typing indicators
      socket.on('typing-start', (data: { chatId: string; receiverId: string }) => {
        socket.to(`user_${data.receiverId}`).emit('user-typing', {
          chatId: data.chatId,
          userId,
          username: user.username
        });
      });

      socket.on('typing-stop', (data: { chatId: string; receiverId: string }) => {
        socket.to(`user_${data.receiverId}`).emit('user-stopped-typing', {
          chatId: data.chatId,
          userId
        });
      });

      // Handle disconnection
      socket.on('disconnect', async () => {
        console.log(`User ${userId} disconnected`);

        try {
          // Update user offline status
          await User.findByIdAndUpdate(userId, { isOnline: false });

          // Remove from online users
          onlineUsers.delete(userId);

          // Notify other users about offline status
          socket.broadcast.emit('user-status-changed', {
            userId,
            isOnline: false
          });

        } catch (error) {
          console.error('Error handling disconnect:', error);
        }
      });

    } catch (error) {
      console.error('Error in socket connection:', error);
      socket.disconnect();
    }
  });

  return io;
};

// Get online users (utility function)
export const getOnlineUsers = (): IOnlineUser[] => {
  return Array.from(onlineUsers.values());
};

// Check if user is online
export const isUserOnline = (userId: string): boolean => {
  return onlineUsers.has(userId);
};
