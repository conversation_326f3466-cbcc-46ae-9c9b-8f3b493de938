# Post API Documentation

## Overview
The Post API provides comprehensive CRUD operations for managing user posts with media uploads, likes, comments, and comment likes. All endpoints require JWT authentication and support automatic data cleanup.

## Base URL
```
http://localhost:5001/api/posts
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Data Models

### Post Schema
```typescript
{
  userId: ObjectId (ref: User),
  description: String (required),
  media: [String] (optional, array of S3 URLs),
  likes: [ObjectId] (ref: User),
  comments: [Comment],
  createdAt: Date,
  updatedAt: Date
}
```

### Comment Schema
```typescript
{
  userId: ObjectId (ref: User),
  content: String (required),
  likes: [ObjectId] (ref: User),
  createdAt: Date,
  updatedAt: Date
}
```

## Endpoints

### 1. Create Post
**POST** `/create`

Creates a new post with optional media files.

**Content-Type:** `multipart/form-data`

**Body Parameters:**
- `description` (string, required): Post description
- `media` (files, optional): Array of image/video files (max 10 files, 50MB each)

**Response:**
```json
{
  "message": "Post created successfully",
  "post": {
    "_id": "post_id",
    "userId": "user_id",
    "description": "Post description",
    "media": ["s3_url1", "s3_url2"],
    "likes": [],
    "comments": [],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. Get User Posts
**GET** `/my-posts`

Retrieves all posts for the authenticated user.

**Response:**
```json
{
  "message": "Posts retrieved successfully",
  "posts": [/* array of posts */],
  "count": 5
}
```

### 3. Get Post by ID
**GET** `/:postId`

Retrieves a specific post by ID (only user's own posts).

**Response:**
```json
{
  "message": "Post retrieved successfully",
  "post": {/* post object */}
}
```

### 4. Update Post
**PATCH** `/:postId`

Updates only the description of a post.

**Body Parameters:**
- `description` (string, required): New post description

**Response:**
```json
{
  "message": "Post updated successfully",
  "post": {/* updated post object */}
}
```

### 5. Delete Post
**DELETE** `/:postId`

Deletes a post and all associated data (likes, comments, media files from S3).

**Response:**
```json
{
  "message": "Post and all associated data deleted successfully"
}
```

### 6. Like/Unlike Post
**POST** `/:postId/like`

Toggles like status for a post.

**Response:**
```json
{
  "message": "Post liked successfully", // or "Post unliked successfully"
  "likesCount": 5,
  "isLiked": true
}
```

### 7. Add Comment
**POST** `/:postId/comment`

Adds a comment to a post.

**Body Parameters:**
- `content` (string, required): Comment content

**Response:**
```json
{
  "message": "Comment added successfully",
  "comment": {
    "_id": "comment_id",
    "userId": "user_id",
    "content": "Comment content",
    "likes": [],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "commentsCount": 3
}
```

### 8. Like/Unlike Comment
**POST** `/:postId/comment/:commentId/like`

Toggles like status for a comment.

**Response:**
```json
{
  "message": "Comment liked successfully", // or "Comment unliked successfully"
  "likesCount": 2,
  "isLiked": true
}
```

### 9. Get Post Likes Count
**GET** `/:postId/likes/count`

Gets the total likes count for a post.

**Response:**
```json
{
  "message": "Post likes count retrieved successfully",
  "likesCount": 10,
  "isLiked": true
}
```

### 10. Get Post Comments Count
**GET** `/:postId/comments/count`

Gets the total comments count for a post.

**Response:**
```json
{
  "message": "Post comments count retrieved successfully",
  "commentsCount": 5
}
```

### 11. Get Comment Likes Count
**GET** `/:postId/comment/:commentId/likes/count`

Gets the total likes count for a specific comment.

**Response:**
```json
{
  "message": "Comment likes count retrieved successfully",
  "likesCount": 3,
  "isLiked": false
}
```

## Features

### Media Upload
- Supports multiple image and video files
- Files are uploaded to AWS S3
- Automatic file validation (images and videos only)
- Maximum 10 files per post, 50MB per file
- Automatic cleanup when post is deleted

### Data Integrity
- Automatic deletion of all associated data when post is deleted
- Cascading deletion of likes and comments
- Media files are removed from S3 when post is deleted

### Dynamic Updates
- Real-time like/unlike functionality
- Automatic count updates
- Timestamp management for comments

### Security
- JWT authentication required for all endpoints
- Users can only access/modify their own posts
- File upload validation and size limits

## Error Responses

### 400 Bad Request
```json
{
  "message": "Description is required"
}
```

### 401 Unauthorized
```json
{
  "message": "User not authenticated"
}
```

### 404 Not Found
```json
{
  "message": "Post not found"
}
```

### 500 Internal Server Error
```json
{
  "message": "Failed to create post"
}
```
