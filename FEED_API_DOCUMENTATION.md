# Feed API Documentation

## Overview
The Feed API provides personalized feed functionality for users, showing posts from their following and followers. The feed is optimized with pagination, time-based filtering (5 days), and efficient database queries to prevent data duplication.

## Base URL
```
http://localhost:5001/api/feed
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Features
- **Personalized Feed**: Shows posts from users you follow and your followers
- **Time-based Filtering**: Only shows posts from the last 5 days
- **Pagination**: 10 posts per page by default, configurable up to 50
- **Optimized Queries**: Uses MongoDB aggregation for efficient data retrieval
- **No Data Duplication**: Leverages existing post schema with computed fields
- **Real-time Data**: Includes like status, counts, and user information

## Data Models

### Feed Item Response
```typescript
{
  post: {
    _id: string,
    userId: string,
    communityId?: string,
    description: string,
    media?: string[],
    likes: string[],
    comments: Comment[],
    createdAt: Date,
    updatedAt: Date
  },
  author: {
    _id: string,
    username: string,
    profilePhoto: string
  },
  community?: {
    _id: string,
    title: string
  },
  isLiked: boolean,
  likesCount: number,
  commentsCount: number,
  createdAt: Date,
  updatedAt: Date
}
```

### Pagination Info
```typescript
{
  currentPage: number,
  totalPages: number,
  totalPosts: number,
  hasNextPage: boolean,
  hasPreviousPage: boolean,
  limit: number
}
```

## Endpoints

### 1. Get User Feed
**GET** `/api/feed`

Get personalized feed for the authenticated user with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1, minimum: 1)
- `limit` (optional): Posts per page (default: 10, minimum: 1, maximum: 50)

**Example Request:**
```bash
curl -X GET "http://localhost:5001/api/feed?page=1&limit=10" \
  -H "Authorization: Bearer your_jwt_token"
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Retrieved 10 posts for your feed",
  "feed": [
    {
      "post": {
        "_id": "60d5ecb74b24a1234567890a",
        "userId": "60d5ecb74b24a1234567890b",
        "description": "This is a sample post",
        "media": ["https://s3-url/image1.jpg"],
        "likes": ["60d5ecb74b24a1234567890c"],
        "comments": [],
        "createdAt": "2023-06-25T10:30:00.000Z",
        "updatedAt": "2023-06-25T10:30:00.000Z"
      },
      "author": {
        "_id": "60d5ecb74b24a1234567890b",
        "username": "john_doe",
        "profilePhoto": "https://s3-url/profile.jpg"
      },
      "community": {
        "_id": "60d5ecb74b24a1234567890d",
        "title": "Tech Community"
      },
      "isLiked": true,
      "likesCount": 15,
      "commentsCount": 3,
      "createdAt": "2023-06-25T10:30:00.000Z",
      "updatedAt": "2023-06-25T10:30:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalPosts": 47,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "limit": 10
  }
}
```

**Error Responses:**
- **401 Unauthorized**: Missing or invalid JWT token
- **400 Bad Request**: Invalid pagination parameters
- **404 Not Found**: User not found
- **500 Internal Server Error**: Server error

### 2. Refresh Feed
**GET** `/api/feed/refresh`

Refresh feed - returns the first page of the most recent posts. Useful for pull-to-refresh functionality.

**Query Parameters:**
- `limit` (optional): Posts per page (default: 10, minimum: 1, maximum: 50)

**Example Request:**
```bash
curl -X GET "http://localhost:5001/api/feed/refresh?limit=15" \
  -H "Authorization: Bearer your_jwt_token"
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Feed refreshed with 15 posts",
  "feed": [...],
  "pagination": {
    "currentPage": 1,
    "totalPages": 4,
    "totalPosts": 52,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "limit": 15
  }
}
```

### 3. Get Feed Statistics
**GET** `/api/feed/stats`

Get statistics about the user's feed including available posts count and network information.

**Example Request:**
```bash
curl -X GET "http://localhost:5001/api/feed/stats" \
  -H "Authorization: Bearer your_jwt_token"
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Feed statistics retrieved successfully",
  "stats": {
    "totalAvailablePosts": 47,
    "followingCount": 25,
    "followersCount": 18,
    "relevantUsersCount": 35
  }
}
```

## Feed Algorithm

The feed algorithm works as follows:

1. **User Network**: Combines posts from users you follow and your followers
2. **Time Filter**: Only includes posts from the last 5 days
3. **Deduplication**: Removes duplicate users if they appear in both following and followers
4. **Sorting**: Orders posts by creation date (newest first)
5. **Pagination**: Efficiently paginates results using MongoDB aggregation
6. **Optimization**: Uses single aggregation query with lookups for user and community data

## Performance Considerations

- **Efficient Queries**: Uses MongoDB aggregation pipeline for optimal performance
- **Index Requirements**: Ensure indexes on `userId`, `createdAt` in posts collection
- **Memory Usage**: Pagination prevents loading all posts at once
- **Caching**: Consider implementing Redis caching for frequently accessed feeds
- **Rate Limiting**: Built-in rate limiting prevents API abuse

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message (in development)"
}
```

## Usage Examples

### Frontend Integration
```javascript
// Get first page of feed
const response = await fetch('/api/feed?page=1&limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const feedData = await response.json();

// Load more posts (pagination)
const nextPage = await fetch(`/api/feed?page=${currentPage + 1}&limit=10`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Refresh feed
const refreshed = await fetch('/api/feed/refresh?limit=10', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## Related APIs
- **Posts API**: `/api/posts` - For creating, updating, and managing posts
- **Follow API**: `/api/follow` - For managing following/followers relationships
- **User API**: `/api/profile` - For user profile management
