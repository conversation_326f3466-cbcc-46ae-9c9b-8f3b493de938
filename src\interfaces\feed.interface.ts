import { Document } from 'mongoose';
import { IPost } from './post.interface';

export interface IFeedItem {
  post: IPost;
  author: {
    _id: string;
    username: string;
    profilePhoto: string;
  };
  community?: {
    _id: string;
    title: string;
  };
  isLiked: boolean;
  likesCount: number;
  commentsCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IPaginationInfo {
  currentPage: number;
  totalPages: number;
  totalPosts: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  limit: number;
}

export interface IFeedResponse {
  success: boolean;
  message: string;
  feed: IFeedItem[];
  pagination: IPaginationInfo;
}

export interface IFeedQuery {
  userId: string;
  page?: number;
  limit?: number;
}

export interface IFeedFilters {
  maxDaysOld: number;
  includeFollowing: boolean;
  includeFollowers: boolean;
}
