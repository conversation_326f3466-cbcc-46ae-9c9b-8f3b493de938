import { Document } from 'mongoose';

export interface ITask extends Document {
  userId: string;
  title: string;
  category: string[];
  date: Date;
  startTime: string;
  endTime: string;
  description: string;
  toDoList: IToDoItem[];
  reminder: string;
  taskCompletionPercentage: number;
  createdAt: Date;
  updatedAt: Date;
  breaks: string[];
}

export interface IToDoItem {
  toDoTask: string;
  isCompleted: boolean;
}
