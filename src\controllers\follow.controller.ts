import type { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import mongoose from 'mongoose';
import User from '../models/User';
import { AuthenticatedRequest } from '../middlewares/auth';

// Follow a user
export const followUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userIdToFollow } = req.body;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate userIdToFollow
    if (!userIdToFollow || !mongoose.Types.ObjectId.isValid(userIdToFollow)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid user ID to follow is required'
      });
      return;
    }

    // Check if trying to follow themselves
    if (currentUserId === userIdToFollow) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Cannot follow yourself'
      });
      return;
    }

    // Get both users
    const [currentUser, userToFollow] = await Promise.all([
      User.findById(currentUserId),
      User.findById(userIdToFollow)
    ]);

    if (!currentUser || !userToFollow) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Check if already following
    const isAlreadyFollowing = currentUser.following.some(
      (followingItem) => followingItem.userId.toString() === userIdToFollow
    );

    if (isAlreadyFollowing) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Already following this user'
      });
      return;
    }

    // Check if current user has blocked the user to follow
    const hasBlockedUser = currentUser.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === userIdToFollow && blockedItem.isBlocked
    );

    if (hasBlockedUser) {
      res.status(StatusCodes.FORBIDDEN).json({
        message: 'Cannot follow a user you have blocked'
      });
      return;
    }

    // Check if current user is blocked by the user to follow
    const isBlockedByUser = userToFollow.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === currentUserId && blockedItem.isBlocked
    );

    if (isBlockedByUser) {
      res.status(StatusCodes.FORBIDDEN).json({
        message: 'Cannot follow this user as you are blocked by them'
      });
      return;
    }

    // Add to current user's following list
    currentUser.following.push({
      userId: userIdToFollow,
      isFollowing: true
    });
    currentUser.followingCount += 1;

    // Add to target user's followers list
    userToFollow.followers.push({
      userId: currentUserId,
      isFollower: true
    });
    userToFollow.followersCount += 1;

    // Save both users
    await Promise.all([
      currentUser.save(),
      userToFollow.save()
    ]);

    res.status(StatusCodes.OK).json({
      message: 'Successfully followed user',
      followingCount: currentUser.followingCount,
      followedUser: {
        userId: userToFollow._id,
        username: userToFollow.username || ""
      }
    });
  } catch (error) {
    console.error('Error following user:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error following user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Unfollow a user
export const unfollowUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { userIdToUnfollow } = req.body;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate userIdToUnfollow
    if (!userIdToUnfollow || !mongoose.Types.ObjectId.isValid(userIdToUnfollow)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid user ID to unfollow is required'
      });
      return;
    }

    // Get both users
    const [currentUser, userToUnfollow] = await Promise.all([
      User.findById(currentUserId),
      User.findById(userIdToUnfollow)
    ]);

    if (!currentUser || !userToUnfollow) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Check if currently following
    const followingIndex = currentUser.following.findIndex(
      (followingItem) => followingItem.userId.toString() === userIdToUnfollow
    );

    if (followingIndex === -1) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Not following this user'
      });
      return;
    }

    // Remove from current user's following list
    currentUser.following.splice(followingIndex, 1);
    currentUser.followingCount = Math.max(0, currentUser.followingCount - 1);

    // Remove from target user's followers list
    const followerIndex = userToUnfollow.followers.findIndex(
      (followerItem) => followerItem.userId.toString() === currentUserId
    );

    if (followerIndex !== -1) {
      userToUnfollow.followers.splice(followerIndex, 1);
      userToUnfollow.followersCount = Math.max(0, userToUnfollow.followersCount - 1);
    }

    // Save both users
    await Promise.all([
      currentUser.save(),
      userToUnfollow.save()
    ]);

    res.status(StatusCodes.OK).json({
      message: 'Successfully unfollowed user',
      followingCount: currentUser.followingCount,
      unfollowedUser: {
        userId: userToUnfollow._id,
        username: userToUnfollow.username || ""
      }
    });
  } catch (error) {
    console.error('Error unfollowing user:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error unfollowing user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get followers list
// Get followers list
export const getFollowers = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Retrieve the user data including blocked users to filter them out
    const user = await User.findById(userId).select('followers followersCount blockedUsers');

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Filter out blocked users from followers list
    const filteredFollowers = user.followers.filter(follower => {
      const isBlocked = user.blockedUsers.some(
        (blockedItem) => blockedItem.userId.toString() === follower.userId.toString() && blockedItem.isBlocked
      );
      return !isBlocked;
    });

    // Fetch followers' usernames dynamically by using their userId
    const followersList = await Promise.all(filteredFollowers.map(async (follower) => {
      // Retrieve the follower's user data to get the username
      const followerUser = await User.findById(follower.userId).select('username');
      return {
        userId: follower.userId,
        usernameFollower: followerUser ? followerUser.username : 'Unknown'
      };
    }));

    res.status(StatusCodes.OK).json({
      message: 'Followers retrieved successfully',
      followers: followersList,
      followersCount: followersList.length
    });
  } catch (error) {
    console.error('Error getting followers:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error getting followers',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};


// Get following list
export const getFollowing = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const user = await User.findById(userId).select('following followingCount blockedUsers');

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Filter out blocked users from following list
    const filteredFollowing = user.following.filter(following => {
      const isBlocked = user.blockedUsers.some(
        (blockedItem) => blockedItem.userId.toString() === following.userId.toString() && blockedItem.isBlocked
      );
      return !isBlocked;
    });

    // Fetch following users' usernames dynamically by using their userId
    const followingList = await Promise.all(filteredFollowing.map(async (following) => {
      // Retrieve the followed user's data to get the username
      const followedUser = await User.findById(following.userId).select('username');
      return {
        userId: following.userId,
        usernameFollowing: followedUser ? followedUser.username : 'Unknown'
      };
    }));

    res.status(StatusCodes.OK).json({
      message: 'Following list retrieved successfully',
      following: followingList,
      followingCount: followingList.length
    });
  } catch (error) {
    console.error('Error getting following list:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error getting following list',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Remove a follower
export const removeFollower = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { followerIdToRemove } = req.body;
    const currentUserId = req.user?.userId;

    if (!currentUserId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate followerIdToRemove
    if (!followerIdToRemove || !mongoose.Types.ObjectId.isValid(followerIdToRemove)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid follower ID to remove is required'
      });
      return;
    }

    // Get both users
    const [currentUser, followerToRemove] = await Promise.all([
      User.findById(currentUserId),
      User.findById(followerIdToRemove)
    ]);

    if (!currentUser || !followerToRemove) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Check if the user is actually a follower
    const followerIndex = currentUser.followers.findIndex(
      (followerItem) => followerItem.userId.toString() === followerIdToRemove
    );

    if (followerIndex === -1) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'This user is not following you'
      });
      return;
    }

    // Remove from current user's followers list
    currentUser.followers.splice(followerIndex, 1);
    currentUser.followersCount = Math.max(0, currentUser.followersCount - 1);

    // Remove from follower's following list
    const followingIndex = followerToRemove.following.findIndex(
      (followingItem) => followingItem.userId.toString() === currentUserId
    );

    if (followingIndex !== -1) {
      followerToRemove.following.splice(followingIndex, 1);
      followerToRemove.followingCount = Math.max(0, followerToRemove.followingCount - 1);
    }

    // Save both users
    await Promise.all([
      currentUser.save(),
      followerToRemove.save()
    ]);

    res.status(StatusCodes.OK).json({
      message: 'Follower removed successfully',
      followersCount: currentUser.followersCount,
      removedFollower: {
        userId: followerToRemove._id,
        username: followerToRemove.username || followerToRemove.email
      }
    });
  } catch (error) {
    console.error('Error removing follower:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error removing follower',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
