import { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import mongoose from 'mongoose';
import User from '../models/User';
import { AuthenticatedRequest } from '../middlewares/auth';
import { 
  INotificationItem, 
  IMarkAsReadRequest, 
  IDeleteNotificationRequest,
  IGetNotificationsResponse,
  INotificationResponse 
} from '../interfaces/notification.interface';

// Get all notifications for the authenticated user
export const getAllNotifications = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const user = await User.findById(userId).select('notifications');

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Sort notifications by date (most recent first)
    const sortedNotifications = user.notifications.sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    const totalCount = sortedNotifications.length;
    const unreadCount = sortedNotifications.filter(notification => !notification.isRead).length;

    const response: IGetNotificationsResponse = {
      message: 'Notifications retrieved successfully',
      notifications: sortedNotifications,
      totalCount,
      unreadCount
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error getting notifications:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error retrieving notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark a specific notification as read
export const markNotificationAsRead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { notificationId } = req.body as IMarkAsReadRequest;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!notificationId || !mongoose.Types.ObjectId.isValid(notificationId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid notification ID is required'
      });
      return;
    }

    const user = await User.findById(userId);

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Find the notification and mark it as read
    const notification = user.notifications.find(
      notif => notif.notificationId.toString() === notificationId
    );

    if (!notification) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Notification not found'
      });
      return;
    }

    notification.isRead = true;
    await user.save();

    const response: INotificationResponse = {
      message: 'Notification marked as read successfully',
      notification
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error marking notification as read',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const user = await User.findById(userId);

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Mark all notifications as read
    user.notifications.forEach(notification => {
      notification.isRead = true;
    });

    await user.save();

    const response: INotificationResponse = {
      message: 'All notifications marked as read successfully',
      notifications: user.notifications,
      totalCount: user.notifications.length,
      unreadCount: 0
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error marking all notifications as read',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete a specific notification
export const deleteNotification = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { notificationId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!notificationId || !mongoose.Types.ObjectId.isValid(notificationId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid notification ID is required'
      });
      return;
    }

    const user = await User.findById(userId);

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Find and remove the notification
    const notificationIndex = user.notifications.findIndex(
      notif => notif.notificationId.toString() === notificationId
    );

    if (notificationIndex === -1) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Notification not found'
      });
      return;
    }

    const deletedNotification = user.notifications[notificationIndex];
    user.notifications.splice(notificationIndex, 1);
    await user.save();

    const response: INotificationResponse = {
      message: 'Notification deleted successfully',
      notification: deletedNotification
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error deleting notification:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error deleting notification',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Clear all notifications for the user
export const clearAllNotifications = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const user = await User.findById(userId);

    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    const deletedCount = user.notifications.length;
    user.notifications = [];
    await user.save();

    const response: INotificationResponse = {
      message: `All notifications cleared successfully. ${deletedCount} notifications were deleted.`,
      notifications: [],
      totalCount: 0,
      unreadCount: 0
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error clearing all notifications:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error clearing all notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
