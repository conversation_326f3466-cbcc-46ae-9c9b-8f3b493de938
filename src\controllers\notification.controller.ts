import { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import mongoose from 'mongoose';
import Notification from '../models/Notification';
import { AuthenticatedRequest } from '../middlewares/auth';

// Get all notifications for the authenticated user
export const getAllNotifications = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Get notifications from separate collection, sorted by creation date (most recent first)
    const notifications = await Notification.find({ userId })
      .sort({ createdAt: -1 });

    const totalCount = notifications.length;

    const response = {
      message: 'Notifications retrieved successfully',
      notifications,
      totalCount
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error getting notifications:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error retrieving notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Note: Mark as read functionality removed as per new simplified schema

// Delete a specific notification
export const deleteNotification = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { notificationId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!notificationId || !mongoose.Types.ObjectId.isValid(notificationId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Valid notification ID is required'
      });
      return;
    }

    // Find and delete the notification from the collection
    const deletedNotification = await Notification.findOneAndDelete({
      _id: notificationId,
      userId
    });

    if (!deletedNotification) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Notification not found'
      });
      return;
    }

    const response = {
      message: 'Notification deleted successfully',
      notification: deletedNotification
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error deleting notification:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error deleting notification',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Clear all notifications for the user
export const clearAllNotifications = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Delete all notifications for the user from the collection
    const deleteResult = await Notification.deleteMany({ userId });
    const deletedCount = deleteResult.deletedCount || 0;

    const response = {
      message: `All notifications cleared successfully. ${deletedCount} notifications were deleted.`,
      notifications: [],
      totalCount: 0
    };

    res.status(StatusCodes.OK).json(response);
  } catch (error) {
    console.error('Error clearing all notifications:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error clearing all notifications',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
