# Block User API Documentation

## Overview
The Block User API provides comprehensive blocking functionality that allows users to block and unblock other users. When a user is blocked, they cannot follow, message, or view the profile of the user who blocked them. The blocking system is integrated across all modules including follow, chat, profile, and feed systems.

## Base URL
```
http://localhost:5001/api/block
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Data Models

### Schema Organization
The blocked user schema is defined in `src/models/Block.ts` and imported into the User model for better code organization.

### User Model Updates
The User model now includes:
```typescript
{
  // ... existing fields
  blockedUsers: [
    {
      userId: ObjectId,
      isBlocked: boolean (default: true),
      blockedAt: Date (default: Date.now)
    }
  ],
  blockedUsersCount: number (default: 0)
}
```

## Endpoints

### 1. Block User
**POST** `/api/block/block`

Block another user. Automatically removes any existing follow relationships between the users.

**Request Body:**
```json
{
  "userIdToBlock": "60d5ecb74b24a1234567890a"
}
```

**Success Response (200):**
```json
{
  "message": "User blocked successfully",
  "blockedUsersCount": 1,
  "blockedUser": {
    "userId": "60d5ecb74b24a1234567890a",
    "username": "john_doe"
  }
}
```

**Error Responses:**
- **400 Bad Request:** Invalid user ID, trying to block yourself, or user already blocked
- **401 Unauthorized:** User not authenticated
- **404 Not Found:** User to block not found
- **500 Internal Server Error:** Server error

### 2. Unblock User
**POST** `/api/block/unblock`

Unblock a previously blocked user.

**Request Body:**
```json
{
  "userIdToUnblock": "60d5ecb74b24a1234567890a"
}
```

**Success Response (200):**
```json
{
  "message": "User unblocked successfully",
  "blockedUsersCount": 0
}
```

**Error Responses:**
- **400 Bad Request:** Invalid user ID or user is not blocked
- **401 Unauthorized:** User not authenticated
- **404 Not Found:** User not found
- **500 Internal Server Error:** Server error

### 3. Get Blocked Users List
**GET** `/api/block/blocked-users`

Retrieve the list of users you have blocked with pagination support.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of users per page (default: 10)

**Success Response (200):**
```json
{
  "message": "Blocked users retrieved successfully",
  "blockedUsers": [
    {
      "userId": {
        "_id": "60d5ecb74b24a1234567890a",
        "username": "john_doe",
        "profilePhoto": "https://example.com/photo.jpg",
        "email": "<EMAIL>"
      },
      "blockedAt": "2023-12-01T10:30:00.000Z"
    }
  ],
  "totalCount": 1,
  "currentPage": 1,
  "totalPages": 1
}
```

**Error Responses:**
- **401 Unauthorized:** User not authenticated
- **404 Not Found:** User not found
- **500 Internal Server Error:** Server error

### 4. Check Block Status
**GET** `/api/block/status/:userId`

Check the blocking relationship status between you and another user.

**URL Parameters:**
- `userId`: The ID of the user to check block status with

**Success Response (200):**
```json
{
  "message": "Block status retrieved successfully",
  "blockStatus": {
    "isBlocked": false,
    "isBlockedBy": true
  }
}
```

**Response Fields:**
- `isBlocked`: Whether you have blocked the other user
- `isBlockedBy`: Whether you are blocked by the other user

**Error Responses:**
- **400 Bad Request:** Invalid user ID
- **401 Unauthorized:** User not authenticated
- **404 Not Found:** User not found
- **500 Internal Server Error:** Server error

## Integration with Other Modules

### Follow System Integration
- **Blocking automatically removes follow relationships:** When User A blocks User B, any existing follow relationships between them are automatically removed
- **Blocked users cannot follow:** Users cannot follow someone they have blocked or someone who has blocked them
- **Followers/Following lists filter blocked users:** The get followers and get following endpoints automatically filter out blocked users

### Chat System Integration
- **Cannot start chats with blocked users:** Users cannot initiate new chats with blocked users
- **Existing chats are hidden:** Chats with blocked users are filtered out from the user's chat list
- **Cannot send messages:** Real-time messaging is blocked between users with blocking relationships
- **Cannot join chat rooms:** Socket connections prevent blocked users from joining each other's chat rooms

### Profile System Integration
- **Cannot view blocked user profiles:** Users cannot view the profiles of users they have blocked
- **Profile access denied when blocked:** Users cannot view profiles of users who have blocked them
- **New endpoint for viewing other profiles:** Added `/api/profile/user/:userId` endpoint with block restrictions

### Feed System Integration
- **Posts filtered from feed:** Posts from blocked users are automatically filtered out of the user's feed
- **Mutual blocking respected:** Posts are also filtered if the post author has blocked the current user

## Usage Examples

### JavaScript/Node.js Example
```javascript
// Block a user
const blockUser = async (userIdToBlock, token) => {
  try {
    const response = await fetch('http://localhost:5001/api/block/block', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ userIdToBlock })
    });
    
    const data = await response.json();
    console.log('User blocked:', data);
  } catch (error) {
    console.error('Error blocking user:', error);
  }
};

// Check block status
const checkBlockStatus = async (userId, token) => {
  try {
    const response = await fetch(`http://localhost:5001/api/block/status/${userId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const data = await response.json();
    console.log('Block status:', data.blockStatus);
  } catch (error) {
    console.error('Error checking block status:', error);
  }
};

// Get blocked users list
const getBlockedUsers = async (token, page = 1, limit = 10) => {
  try {
    const response = await fetch(`http://localhost:5001/api/block/blocked-users?page=${page}&limit=${limit}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const data = await response.json();
    console.log('Blocked users:', data.blockedUsers);
  } catch (error) {
    console.error('Error getting blocked users:', error);
  }
};
```

## Security Considerations

1. **Authentication Required:** All endpoints require valid JWT authentication
2. **User Validation:** All user IDs are validated before processing
3. **Self-blocking Prevention:** Users cannot block themselves
4. **Data Privacy:** Blocked users cannot access each other's data across all modules
5. **Automatic Cleanup:** Blocking automatically removes existing relationships (follows, etc.)

## Performance Optimizations

1. **Indexed Queries:** Block relationships are indexed for efficient lookups
2. **Batch Operations:** Multiple relationship cleanups are performed in parallel
3. **Filtered Aggregations:** Feed and other list endpoints use database-level filtering
4. **Minimal Data Transfer:** Only necessary user information is returned in responses

## Error Handling

All endpoints follow consistent error response patterns:
- **4xx errors:** Client-side issues (authentication, validation, etc.)
- **5xx errors:** Server-side issues
- **Detailed messages:** Clear error messages for debugging
- **Consistent format:** All error responses follow the same JSON structure

## Rate Limiting

Block operations are subject to the application's global rate limiting to prevent abuse.

## Testing

Use the provided endpoints to test blocking functionality:
1. Block a user and verify follow relationships are removed
2. Try to follow a blocked user (should fail)
3. Check that blocked users don't appear in feeds
4. Verify chat restrictions work correctly
5. Test profile access restrictions
