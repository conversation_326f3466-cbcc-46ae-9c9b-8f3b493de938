import mongoose from 'mongoose';
import User from '../../models/User';
import { ICreateNotificationRequest } from '../../interfaces/notification.interface';

// Helper function to create a notification for a user
export const createNotification = async (data: ICreateNotificationRequest): Promise<void> => {
  try {
    const { userId, message, type, relatedData } = data;

    // Validate userId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.error('Invalid userId provided for notification:', userId);
      return;
    }

    const user = await User.findById(userId);
    if (!user) {
      console.error('User not found for notification:', userId);
      return;
    }

    // Create notification object
    const notification = {
      notificationId: new mongoose.Types.ObjectId(),
      message,
      type,
      isRead: false,
      date: new Date(),
      time: new Date().toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      }),
      relatedData: relatedData ? {
        taskId: relatedData.taskId ? new mongoose.Types.ObjectId(relatedData.taskId) : undefined,
        habitId: relatedData.habitId ? new mongoose.Types.ObjectId(relatedData.habitId) : undefined,
        fromUserId: relatedData.fromUserId ? new mongoose.Types.ObjectId(relatedData.fromUserId) : undefined,
        fromUsername: relatedData.fromUsername
      } : undefined
    };

    // Add notification to user's notifications array
    user.notifications.push(notification);
    await user.save();

    console.log(`Notification created for user ${userId}: ${message}`);
  } catch (error) {
    console.error('Error creating notification:', error);
  }
};

// Notification helper functions for different events

export const createSignupNotification = async (userId: string): Promise<void> => {
  await createNotification({
    userId,
    message: 'Welcome to FlowCus! Your account has been created successfully.',
    type: 'signup'
  });
};

export const createTaskCompletedNotification = async (userId: string, taskTitle: string, taskId: string): Promise<void> => {
  await createNotification({
    userId,
    message: `Congratulations! You have completed the task: "${taskTitle}".`,
    type: 'task_completed',
    relatedData: {
      taskId
    }
  });
};

export const createHabitCompletedNotification = async (userId: string, habitTitle: string, habitId: string): Promise<void> => {
  await createNotification({
    userId,
    message: `Great job! You have completed your habit: "${habitTitle}" for today.`,
    type: 'habit_completed',
    relatedData: {
      habitId
    }
  });
};

export const createTaskEndedNotification = async (userId: string, taskTitle: string, taskId: string): Promise<void> => {
  await createNotification({
    userId,
    message: `Your task "${taskTitle}" has ended. Check your progress and plan your next steps.`,
    type: 'task_ended',
    relatedData: {
      taskId
    }
  });
};

export const createPasswordChangedNotification = async (userId: string): Promise<void> => {
  await createNotification({
    userId,
    message: 'Your password has been changed successfully. If this wasn\'t you, please contact support.',
    type: 'password_changed'
  });
};

export const createUserFollowedNotification = async (
  userId: string, 
  followerUsername: string, 
  followerId: string
): Promise<void> => {
  await createNotification({
    userId,
    message: `${followerUsername} started following you.`,
    type: 'user_followed',
    relatedData: {
      fromUserId: followerId,
      fromUsername: followerUsername
    }
  });
};

export const createGeneralNotification = async (userId: string, message: string): Promise<void> => {
  await createNotification({
    userId,
    message,
    type: 'general'
  });
};

// Helper function to create notifications for multiple users (useful for system-wide notifications)
export const createBulkNotifications = async (
  userIds: string[], 
  message: string, 
  type: 'general' = 'general'
): Promise<void> => {
  try {
    const promises = userIds.map(userId => 
      createNotification({
        userId,
        message,
        type
      })
    );
    
    await Promise.all(promises);
    console.log(`Bulk notifications sent to ${userIds.length} users`);
  } catch (error) {
    console.error('Error creating bulk notifications:', error);
  }
};
