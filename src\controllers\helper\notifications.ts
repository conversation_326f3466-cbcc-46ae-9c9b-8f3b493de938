import mongoose from 'mongoose';
import Notification from '../../models/Notification';
import { ICreateNotificationRequest } from '../../interfaces/notification.interface';
import { sendNotificationToUser } from '../../services/pushNotification';

// Helper function to create a notification for a user
export const createNotification = async (data: ICreateNotificationRequest): Promise<void> => {
  try {
    const { userId, text } = data;

    // Validate userId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.error('Invalid userId provided for notification:', userId);
      return;
    }

    // Create notification in separate collection
    const notification = new Notification({
      userId: new mongoose.Types.ObjectId(userId),
      text
    });

    await notification.save();

    // Send push notification if user has notifications enabled
    await sendNotificationToUser(userId, 'FlowCus', text);

    console.log(`Notification created for user ${userId}: ${text}`);
  } catch (error) {
    console.error('Error creating notification:', error);
  }
};

// Notification helper functions for different events

export const createSignupNotification = async (userId: string): Promise<void> => {
  await createNotification({
    userId,
    text: 'Welcome to FlowCus! Your account has been created successfully.'
  });
};

export const createTaskCompletedNotification = async (userId: string, taskTitle: string, taskId: string): Promise<void> => {
  await createNotification({
    userId,
    text: `Congratulations! You have completed the task: "${taskTitle}".`
  });
};

export const createHabitCompletedNotification = async (userId: string, habitTitle: string, habitId: string): Promise<void> => {
  await createNotification({
    userId,
    text: `Great job! You have completed your habit: "${habitTitle}" for today.`
  });
};

export const createTaskEndedNotification = async (userId: string, taskTitle: string, taskId: string): Promise<void> => {
  await createNotification({
    userId,
    text: `Your task "${taskTitle}" has ended. Check your progress and plan your next steps.`
  });
};

export const createPasswordChangedNotification = async (userId: string): Promise<void> => {
  await createNotification({
    userId,
    text: 'Your password has been changed successfully. If this wasn\'t you, please contact support.'
  });
};

export const createUserFollowedNotification = async (
  userId: string,
  followerUsername: string,
  followerId: string
): Promise<void> => {
  await createNotification({
    userId,
    text: `${followerUsername} started following you.`
  });
};

export const createGeneralNotification = async (userId: string, text: string): Promise<void> => {
  await createNotification({
    userId,
    text
  });
};

// Helper function to create notifications for multiple users (useful for system-wide notifications)
export const createBulkNotifications = async (
  userIds: string[],
  text: string
): Promise<void> => {
  try {
    const promises = userIds.map(userId =>
      createNotification({
        userId,
        text
      })
    );

    await Promise.all(promises);
    console.log(`Bulk notifications sent to ${userIds.length} users`);
  } catch (error) {
    console.error('Error creating bulk notifications:', error);
  }
};
