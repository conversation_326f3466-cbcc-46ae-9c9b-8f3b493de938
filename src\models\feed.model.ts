import mongoose from 'mongoose';
import Post from './Post';
import User from './User';
import { IFeedItem, IFeedQuery, IPaginationInfo } from '../interfaces/feed.interface';

export class FeedService {
  /**
   * Get personalized feed for a user based on following/followers
   * @param userId - Current user's ID
   * @param page - Page number (default: 1)
   * @param limit - Posts per page (default: 10)
   * @returns Promise with feed items and pagination info
   */
  static async getUserFeed(userId: string, page: number = 1, limit: number = 1) {
    try {
      // Get current user with following/followers data and blocked users
      const currentUser = await User.findById(userId).select('following followers blockedUsers');

      if (!currentUser) {
        throw new Error('User not found');
      }

      // Extract user IDs from following and followers arrays
      const followingUserIds = currentUser.following
        .filter(item => item.isFollowing)
        .map(item => item.userId);

      const followerUserIds = currentUser.followers
        .filter(item => item.isFollower)
        .map(item => item.userId);

      // Get blocked user IDs
      const blockedUserIds = currentUser.blockedUsers
        .filter(item => item.isBlocked)
        .map(item => item.userId.toString());

      // Combine and deduplicate user IDs (following + followers)
      let relevantUserIds = [...new Set([
        ...followingUserIds.map(id => id.toString()),
        ...followerUserIds.map(id => id.toString())
      ])];

      // Filter out blocked users from relevant user IDs
      relevantUserIds = relevantUserIds.filter(id => !blockedUserIds.includes(id));

      // Calculate date filter (5 days ago)
      const fiveDaysAgo = new Date();
      fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build aggregation pipeline for optimized query
      const pipeline = [
        // Match posts from relevant users within 5 days
        {
          $match: {
            userId: { $in: relevantUserIds.map(id => new mongoose.Types.ObjectId(id)) },
            createdAt: { $gte: fiveDaysAgo }
          }
        },
        // Sort by creation date (newest first)
        {
          $sort: { createdAt: -1 as -1}
        },
        // Lookup user information
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'author',
            pipeline: [
              {
                $project: {
                  username: 1,
                  profilePhoto: 1
                }
              }
            ]
          }
        },
        // Lookup community information (if exists)
        {
          $lookup: {
            from: 'communities',
            localField: 'communityId',
            foreignField: '_id',
            as: 'community',
            pipeline: [
              {
                $project: {
                  title: 1
                }
              }
            ]
          }
        },
        // Add computed fields
        {
          $addFields: {
            author: { $arrayElemAt: ['$author', 0] },
            community: { $arrayElemAt: ['$community', 0] },
            isLiked: {
              $in: [new mongoose.Types.ObjectId(userId), '$likes']
            },
            likesCount: { $size: '$likes' },
            commentsCount: { $size: '$comments' }
          }
        },
        // Project final structure
        {
          $project: {
            _id: 1,
            userId: 1,
            communityId: 1,
            description: 1,
            media: 1,
            likes: 1,
            comments: 1,
            createdAt: 1,
            updatedAt: 1,
            author: 1,
            community: 1,
            isLiked: 1,
            likesCount: 1,
            commentsCount: 1
          }
        }
      ];

      // Get total count for pagination
      const totalCountPipeline = [
        ...pipeline.slice(0, 1), // Only match stage
        { $count: 'total' }
      ];

      const [posts, totalCountResult] = await Promise.all([
        Post.aggregate([
          ...pipeline,
          { $skip: skip },
          { $limit: limit }
        ]),
        Post.aggregate(totalCountPipeline)
      ]);

      // Additional filtering: Remove posts from users who have blocked the current user
      // This requires checking each post author's blocked users list
      const filteredPosts = [];
      for (const post of posts) {
        const postAuthor = await User.findById(post.userId).select('blockedUsers');
        if (postAuthor) {
          const isBlockedByAuthor = postAuthor.blockedUsers.some(
            (blockedItem: any) => blockedItem.userId.toString() === userId && blockedItem.isBlocked
          );
          if (!isBlockedByAuthor) {
            filteredPosts.push(post);
          }
        }
      }

      const totalPosts = totalCountResult.length > 0 ? totalCountResult[0].total : 0;
      const totalPages = Math.ceil(totalPosts / limit);

      // Build pagination info
      const pagination: IPaginationInfo = {
        currentPage: page,
        totalPages,
        totalPosts,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
        limit
      };

      // Transform filtered posts to feed items
      const feedItems: IFeedItem[] = filteredPosts.map(post => ({
        post: {
          _id: post._id,
          userId: post.userId,
          communityId: post.communityId,
          description: post.description,
          media: post.media || [],
          likes: post.likes,
          comments: post.comments,
          createdAt: post.createdAt,
          updatedAt: post.updatedAt
        } as any,
        author: post.author,
        community: post.community,
        isLiked: post.isLiked,
        likesCount: post.likesCount,
        commentsCount: post.commentsCount,
        createdAt: post.createdAt,
        updatedAt: post.updatedAt
      }));

      return {
        feedItems,
        pagination
      };

    } catch (error) {
      console.error('Error in getUserFeed:', error);
      throw error;
    }
  }

  /**
   * Get feed statistics for a user
   * @param userId - User's ID
   * @returns Promise with feed statistics
   */
  static async getFeedStats(userId: string) {
    try {
      const currentUser = await User.findById(userId).select('following followers');
      
      if (!currentUser) {
        throw new Error('User not found');
      }

      const followingUserIds = currentUser.following
        .filter(item => item.isFollowing)
        .map(item => item.userId);
      
      const followerUserIds = currentUser.followers
        .filter(item => item.isFollower)
        .map(item => item.userId);

      const relevantUserIds = [...new Set([
        ...followingUserIds.map(id => id.toString()),
        ...followerUserIds.map(id => id.toString())
      ])];

      const fiveDaysAgo = new Date();
      fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

      const totalAvailablePosts = await Post.countDocuments({
        userId: { $in: relevantUserIds.map(id => new mongoose.Types.ObjectId(id)) },
        createdAt: { $gte: fiveDaysAgo }
      });

      return {
        totalAvailablePosts,
        followingCount: followingUserIds.length,
        followersCount: followerUserIds.length,
        relevantUsersCount: relevantUserIds.length
      };

    } catch (error) {
      console.error('Error in getFeedStats:', error);
      throw error;
    }
  }
}

export default FeedService;
