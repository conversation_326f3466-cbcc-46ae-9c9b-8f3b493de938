# Chat API Documentation

## Overview
The Chat API provides real-time messaging functionality using Socket.IO and REST endpoints with JWT authentication. Users can send text messages and media files (up to 5 images/videos per message), track online status, and manage read receipts.

## Base URL
```
http://localhost:5001/api/chat
```

## Socket.IO Connection
```
ws://localhost:5001
```

## Authentication
All endpoints and socket connections require JWT authentication. Include the token in:
- REST API: Authorization header: `Authorization: Bearer <your_jwt_token>`
- Socket.IO: Authentication object: `{ auth: { token: '<your_jwt_token>' } }`

## REST API Endpoints

### 1. Start or Get Chat
**POST** `/api/chat/start`

Start a new chat or get existing chat with another user.

**Request Body:**
```json
{
  "receiverUserId": "user_id_here"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Chat created successfully",
  "data": {
    "chatId": "chat_id_here",
    "participant": {
      "userId": "user_id",
      "username": "username",
      "email": "<EMAIL>",
      "profilePhoto": "photo_url",
      "isOnline": true
    }
  }
}
```

### 2. Get User Chats
**GET** `/api/chat/user-chats`

Get all chats for the authenticated user.

**Response:**
```json
{
  "success": true,
  "message": "Chats retrieved successfully",
  "data": {
    "chats": [
      {
        "chatId": "chat_id",
        "participant": {
          "userId": "user_id",
          "username": "username",
          "email": "<EMAIL>",
          "profilePhoto": "photo_url",
          "isOnline": true
        },
        "lastMessage": "Hello there!",
        "lastMessageAt": "2024-01-01T12:00:00Z",
        "unreadCount": 3
      }
    ]
  }
}
```

### 3. Get Chat Messages
**GET** `/api/chat/:chatId/messages?page=1&limit=50`

Get messages for a specific chat with pagination.

**Response:**
```json
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "chatId": "chat_id",
    "participant": {
      "userId": "user_id",
      "username": "username",
      "email": "<EMAIL>",
      "profilePhoto": "photo_url",
      "isOnline": true
    },
    "messages": [
      {
        "_id": "message_id",
        "chatId": "chat_id",
        "senderId": "sender_id",
        "receiverId": "receiver_id",
        "message": "Hello!",
        "media": ["url1", "url2"],
        "messageType": "mixed",
        "isRead": true,
        "sentAt": "2024-01-01T12:00:00Z",
        "readAt": "2024-01-01T12:05:00Z"
      }
    ],
    "hasMore": false,
    "page": 1,
    "totalPages": 1
  }
}
```

### 4. Mark Messages as Read
**PUT** `/api/chat/mark-read`

Mark specific messages as read.

**Request Body:**
```json
{
  "chatId": "chat_id_here",
  "messageIds": ["message_id_1", "message_id_2"]
}
```

### 5. Upload Chat Media
**POST** `/api/chat/upload-media`

Upload media files for chat (up to 5 files, 50MB each).

**Request:** Multipart form data with field name `media`

**Response:**
```json
{
  "success": true,
  "message": "Media uploaded successfully",
  "data": {
    "mediaUrls": ["url1", "url2", "url3"]
  }
}
```

## Socket.IO Events

### Client to Server Events

#### 1. join-chat
Join a specific chat room to receive real-time messages.
```javascript
socket.emit('join-chat', chatId);
```

#### 2. leave-chat
Leave a specific chat room.
```javascript
socket.emit('leave-chat', chatId);
```

#### 3. send-message
Send a message in a chat.
```javascript
socket.emit('send-message', {
  chatId: 'chat_id',
  message: 'Hello there!',
  media: ['url1', 'url2'], // Optional
  messageType: 'mixed' // 'text', 'media', or 'mixed'
});
```

#### 4. mark-as-read
Mark messages as read.
```javascript
socket.emit('mark-as-read', {
  chatId: 'chat_id',
  messageIds: ['msg_id_1', 'msg_id_2']
});
```

#### 5. typing-start
Indicate user started typing.
```javascript
socket.emit('typing-start', {
  chatId: 'chat_id',
  receiverId: 'receiver_user_id'
});
```

#### 6. typing-stop
Indicate user stopped typing.
```javascript
socket.emit('typing-stop', {
  chatId: 'chat_id',
  receiverId: 'receiver_user_id'
});
```

### Server to Client Events

#### 1. message-received
Receive a new message.
```javascript
socket.on('message-received', (message) => {
  console.log('New message:', message);
});
```

#### 2. message-read
Notification that messages were read.
```javascript
socket.on('message-read', (data) => {
  console.log('Messages read:', data);
});
```

#### 3. user-typing
User started typing notification.
```javascript
socket.on('user-typing', (data) => {
  console.log(`${data.username} is typing in chat ${data.chatId}`);
});
```

#### 4. user-stopped-typing
User stopped typing notification.
```javascript
socket.on('user-stopped-typing', (data) => {
  console.log(`User stopped typing in chat ${data.chatId}`);
});
```

#### 5. user-status-changed
User online/offline status changed.
```javascript
socket.on('user-status-changed', (data) => {
  console.log(`User ${data.userId} is now ${data.isOnline ? 'online' : 'offline'}`);
});
```

#### 6. chat-updated
Chat was updated (new message, etc.).
```javascript
socket.on('chat-updated', (data) => {
  console.log('Chat updated:', data);
});
```

#### 7. error
Error occurred.
```javascript
socket.on('error', (error) => {
  console.error('Socket error:', error);
});
```

## Client-Side Integration Example

```javascript
import io from 'socket.io-client';

// Connect to socket with authentication
const socket = io('http://localhost:5001', {
  auth: {
    token: 'your_jwt_token_here'
  }
});

// Handle connection
socket.on('connect', () => {
  console.log('Connected to chat server');
  
  // Join a specific chat
  socket.emit('join-chat', 'chat_id_here');
});

// Listen for new messages
socket.on('message-received', (message) => {
  console.log('New message received:', message);
  // Update UI with new message
});

// Listen for online status changes
socket.on('user-status-changed', (data) => {
  console.log(`User ${data.userId} is ${data.isOnline ? 'online' : 'offline'}`);
  // Update UI to show online status
});

// Send a message
const sendMessage = (chatId, messageText, mediaUrls = []) => {
  socket.emit('send-message', {
    chatId,
    message: messageText,
    media: mediaUrls,
    messageType: mediaUrls.length > 0 ? (messageText ? 'mixed' : 'media') : 'text'
  });
};

// Handle typing indicators
const startTyping = (chatId, receiverId) => {
  socket.emit('typing-start', { chatId, receiverId });
};

const stopTyping = (chatId, receiverId) => {
  socket.emit('typing-stop', { chatId, receiverId });
};
```

## Online Status Detection

The system automatically tracks user online status using:
1. Socket.IO connection/disconnection events
2. The `isOnline` field in the user schema
3. Real-time status updates via socket events

For client-side online detection, you can use:
```javascript
// Detect browser online/offline status
window.addEventListener('online', () => {
  console.log('Browser is online');
  // Reconnect socket if needed
});

window.addEventListener('offline', () => {
  console.log('Browser is offline');
  // Handle offline state
});

// Check current status
console.log('Browser online status:', navigator.onLine);
```

## Message Types

- **text**: Text-only messages
- **media**: Media-only messages (images/videos)
- **mixed**: Messages with both text and media

## File Upload Limits

- Maximum 5 files per message
- Maximum 50MB per file
- Supported formats: Images and videos
- Files are uploaded to AWS S3

## Error Handling

All endpoints return standardized error responses:
```json
{
  "success": false,
  "message": "Error description"
}
```

Common HTTP status codes:
- 200: Success
- 400: Bad Request
- 401: Unauthorized
- 404: Not Found
- 500: Internal Server Error
