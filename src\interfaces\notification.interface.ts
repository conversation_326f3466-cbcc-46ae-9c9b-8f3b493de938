import mongoose from 'mongoose';

export interface INotificationItem {
  notificationId: mongoose.Types.ObjectId;
  message: string;
  type: 'signup' | 'task_completed' | 'habit_completed' | 'task_ended' | 'password_changed' | 'user_followed' | 'general';
  isRead: boolean;
  date: Date;
  time: string;
  relatedData?: {
    taskId?: mongoose.Types.ObjectId;
    habitId?: mongoose.Types.ObjectId;
    fromUserId?: mongoose.Types.ObjectId;
    fromUsername?: string;
  };
}

export interface ICreateNotificationRequest {
  userId: string;
  message: string;
  type: 'signup' | 'task_completed' | 'habit_completed' | 'task_ended' | 'password_changed' | 'user_followed' | 'general';
  relatedData?: {
    taskId?: string;
    habitId?: string;
    fromUserId?: string;
    fromUsername?: string;
  };
}

export interface IMarkAsReadRequest {
  notificationId: string;
}

export interface IDeleteNotificationRequest {
  notificationId: string;
}

export interface IGetNotificationsResponse {
  message: string;
  notifications: INotificationItem[];
  totalCount: number;
  unreadCount: number;
}

export interface INotificationResponse {
  message: string;
  notification?: INotificationItem;
  notifications?: INotificationItem[];
  totalCount?: number;
  unreadCount?: number;
}
