import mongoose from 'mongoose';

export interface INotification {
  _id?: any;
  userId: mongoose.Types.ObjectId;
  text: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateNotificationRequest {
  userId: string;
  text: string;
}

export interface IGetNotificationsResponse {
  message: string;
  notifications: INotification[];
  totalCount: number;
}

export interface INotificationResponse {
  message: string;
  notification?: INotification;
  notifications?: INotification[];
  totalCount?: number;
}

// Push notification interfaces
export interface IPushNotificationPayload {
  notification: {
    title: string;
    body: string;
    sound?: string;
  };
  token: string;
}

export interface IPushNotificationDataPayload {
  data: {
    data: string;
    time: string;
    title: string;
  };
  notification: {
    title: string;
    body: string;
    sound: string;
    android_channel_id: string;
  };
}
