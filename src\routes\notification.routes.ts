import { Router } from 'express';
import {
  getAllNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  clearAllNotifications
} from '../controllers/notification.controller';
import { authenticateToken } from '../middlewares/index';

const router = Router();

// Apply JWT authentication to all routes
router.use(authenticateToken);

// Get all notifications for the authenticated user
router.get('/', getAllNotifications);

// Mark a specific notification as read
router.patch('/mark-as-read', markNotificationAsRead);

// Mark all notifications as read
router.patch('/mark-all-as-read', markAllNotificationsAsRead);

// Delete a specific notification
router.delete('/:notificationId', deleteNotification);

// Clear all notifications
router.delete('/', clearAllNotifications);

export default router;
