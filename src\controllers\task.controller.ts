import { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import Task from '../models/Task';
import { AuthenticatedRequest } from '../middlewares/auth';
import mongoose from 'mongoose';
import { calculateTaskCompletionPercentage } from '../utils/taskCompletion.helper'; 

// Create a new task
export const createTask = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { title, category, date, startTime, endTime, description, toDoList, reminder } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate required fields
    if (!title || !category || !date || !startTime || !endTime || !description || !reminder) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'All required fields must be provided'
      });
      return;
    }

    // Validate category is an array
    if (!Array.isArray(category) || category.length === 0) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Please add at least one category'
      });
      return;
    }

    // Validate toDoList if provided
    if (toDoList && !Array.isArray(toDoList)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Please add at least one to do item'
      });
      return;
    }

    const taskCompletionPercentage = calculateTaskCompletionPercentage(toDoList || []);

    const newTask = new Task({
      userId,
      title,
      category,
      date: new Date(date),
      startTime,
      endTime,
      description,
      toDoList: toDoList || [],
      reminder,
      taskCompletionPercentage
    });

    const savedTask = await newTask.save();
    
    res.status(StatusCodes.CREATED).json({
      message: 'Task created successfully',
      task: savedTask
    });

  } catch (error) {
    console.error('Error creating task:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error creating task',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};


// Get all tasks for the authenticated user
export const getAllTasks = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const tasks = await Task.find({ userId }).sort({ date: 1, startTime: 1 });
    
    res.status(StatusCodes.OK).json({
      message: 'Tasks retrieved successfully',
      tasks,
      count: tasks.length
    });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error fetching tasks',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};



// Get a specific task by ID
export const getTaskById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { taskId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Invalid task ID'
      });
      return;
    }

    const task = await Task.findOne({ _id: taskId, userId });

    if (!task) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Task not found'
      });
      return;
    }

    res.status(StatusCodes.OK).json({
      message: 'Task retrieved successfully',
      task
    });
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error fetching task',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update a task
export const updateTask = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { taskId } = req.params;
    const userId = req.user?.userId;
    const updateData = req.body;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Invalid task ID'
      });
      return;
    }

    // Validate category if provided
    if (updateData.category && (!Array.isArray(updateData.category) || updateData.category.length === 0)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Please add at least one category'
      });
      return;
    }

    // Validate toDoList if provided
    if (updateData.toDoList && !Array.isArray(updateData.toDoList)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Please add at least one to do item'
      });
      return;
    }

    if (updateData.toDoList) {
      updateData.taskCompletionPercentage = calculateTaskCompletionPercentage(updateData.toDoList);
    }

    // Convert date string to Date object if provided
    if (updateData.date) {
      updateData.date = new Date(updateData.date);
    }

    const updatedTask = await Task.findOneAndUpdate(
      { _id: taskId, userId },
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedTask) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Task not found'
      });
      return;
    }

    res.status(StatusCodes.OK).json({
      message: 'Task updated successfully',
      task: updatedTask
    });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error updating task',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete a task
export const deleteTask = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { taskId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Invalid task ID'
      });
      return;
    }

    const deletedTask = await Task.findOneAndDelete({ _id: taskId, userId });

    if (!deletedTask) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Task not found'
      });
      return;
    }

    res.status(StatusCodes.OK).json({
      message: 'Task deleted successfully',
      task: deletedTask
    });
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error deleting task',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
