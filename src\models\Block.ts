import mongoose, { Schema } from 'mongoose';

export const blockedItemSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  isBlocked: { 
    type: Boolean, 
    default: true 
  },
  blockedAt: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

// Index for efficient queries
blockedItemSchema.index({ userId: 1 });
blockedItemSchema.index({ isBlocked: 1 });
