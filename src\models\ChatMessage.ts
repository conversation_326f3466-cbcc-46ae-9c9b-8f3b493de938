import mongoose, { Schema } from 'mongoose';
import { IChatMessage } from '../interfaces/index';

const chatMessageSchema: Schema = new Schema({
  chatId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Chat',
    required: true
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  receiverId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  message: {
    type: String,
    trim: true,
    default: ''
  },
  media: [{
    type: String,
    validate: {
      validator: function(v: string[]) {
        return v.length <= 5; // Maximum 5 media files
      },
      message: 'Maximum 5 media files allowed per message'
    }
  }],
  messageType: {
    type: String,
    enum: ['text', 'media', 'mixed'],
    required: true,
    default: 'text'
  },
  isRead: {
    type: Boolean,
    default: false
  },
  sentAt: {
    type: Date,
    default: Date.now
  },
  readAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
chatMessageSchema.index({ chatId: 1, sentAt: -1 });
chatMessageSchema.index({ senderId: 1 });
chatMessageSchema.index({ receiverId: 1 });
chatMessageSchema.index({ isRead: 1 });

// Validation to ensure either message or media is provided
chatMessageSchema.pre('save', function(next) {
  const mediaArray = this.media as string[];
  if (!this.message && (!mediaArray || mediaArray.length === 0)) {
    next(new Error('Either message text or media must be provided'));
  } else {
    next();
  }
});

const ChatMessage = mongoose.model<IChatMessage>('ChatMessage', chatMessageSchema);

export default ChatMessage;
