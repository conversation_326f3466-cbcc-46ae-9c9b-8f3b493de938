# Habits API Documentation

## Overview
The Habits API provides CRUD operations for managing user habits with automatic daily tracking and synchronization between todoList and habitsList.

## Base URL
```
http://localhost:5001/api/habits
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Endpoints

### 1. Create Habit
**POST** `/api/habits`

**Request Body:**
```json
{
  "title": "Morning Exercise",
  "icon": "🏃‍♂️",
  "description": "30 minutes of morning exercise"
}
```

**Response:**
```json
{
  "message": "Habit created successfully",
  "habit": {
    "_id": "habit_id",
    "userId": "user_id",
    "title": "Morning Exercise",
    "icon": "🏃‍♂️",
    "description": "30 minutes of morning exercise",
    "todoList": [
      {
        "date": "2025-07-14T00:00:00.000Z",
        "isCompleted": false
      }
    ],
    "todoList": [
      {
        "date": "2025-07-14T00:00:00.000Z",
        "isCompleted": false
      }
    ],
    "createdAt": "2025-07-14T10:30:00.000Z",
    "updatedAt": "2025-07-14T10:30:00.000Z"
  }
}
```

### 2. Get All Habits
**GET** `/api/habits`

**Response:**
```json
{
  "message": "Habits retrieved successfully",
  "habits": [
    {
      "_id": "habit_id",
      "userId": "user_id",
      "title": "Morning Exercise",
      "icon": "🏃‍♂️",
      "description": "30 minutes of morning exercise",
      "todoList": [
        {
          "date": "2025-07-14T00:00:00.000Z",
          "isCompleted": false
        }
      ],

      "createdAt": "2025-07-14T10:30:00.000Z",
      "updatedAt": "2025-07-14T10:30:00.000Z"
    }
  ],
  "habitCompletionPercentage": 0,
  "count": 1
}
```

### 3. Get All Habit Titles
**GET** `/api/habits/titles`

**Response:**
```json
{
  "message": "Habit titles retrieved successfully",
  "habitTitles": [
    {
      "id": "habit_id",
      "title": "Morning Exercise"
    }
  ],
  "count": 1
}
```

### 4. Update Habit
**PUT** `/api/habits/:habitId`

**For updating habit details:**
```json
{
  "title": "Updated Morning Exercise",
  "icon": "🏋️‍♂️",
  "description": "45 minutes of morning exercise"
}
```

**For updating completion status:**
```json
{
  "isCompleted": true
}
```

**Response:**
```json
{
  "message": "Habit updated successfully",
  "habit": {
    // Updated habit object
  }
}
```

### 5. Delete Habit
**DELETE** `/api/habits/:habitId`

**Response:**
```json
{
  "message": "Habit deleted successfully",
  "habit": {
    // Deleted habit object
  }
}
```

## Features

### Automatic Daily Tracking
- Each habit automatically gets a new todoList entry every day at 00:01 UTC
- The system ensures no duplicate entries for the same date
- New habits immediately get today's entry upon creation

### Database-Based Habit Management
- All habits are stored individually in the database with their own todoList arrays
- Habit titles and information are retrieved directly from the database by querying all habits for a user
- No centralized habitList - each operation queries the database for the most up-to-date information
- This approach ensures data consistency and eliminates synchronization issues

### Collective Completion Percentage Calculation
- Each user has a single `habitCompletionPercentage` field that represents their overall progress
- The percentage is calculated based on ALL habits' today completion status collectively
- Formula: (Number of habits completed today / Total number of habits) × 100
- Percentage is recalculated whenever any habit's todoList is modified
- This provides a single metric showing the user's overall habit completion for today

### Today-Based Logic
- When `isCompleted` is updated, the habit's todoList for today is updated
- The user's collective completion percentage is recalculated based on all habits' today status
- All habit titles are retrieved by querying all habits from the database for the authenticated user

### Error Handling
All endpoints return appropriate HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `404` - Not Found
- `500` - Internal Server Error

### Validation
- All required fields are validated
- JWT token is required for all operations
- Only the habit owner can perform operations on their habits
- Invalid ObjectIds are rejected with appropriate error messages
