import { Document } from 'mongoose';

export interface IBlockedItem {
  userId: string;
  isBlocked: boolean;
  blockedAt?: Date;
}

export interface IBlock extends Document {
  userId: string;
  blockedUsers: IBlockedItem[];
  blockedUsersCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IBlockStatus {
  isBlocked: boolean;
  isBlockedBy: boolean;
}

export interface IBlockUserRequest {
  userIdToBlock: string;
}

export interface IUnblockUserRequest {
  userIdToUnblock: string;
}

export interface IBlockedUserResponse {
  userId: string;
  username?: string;
  profilePhoto?: string;
  blockedAt: Date;
}
