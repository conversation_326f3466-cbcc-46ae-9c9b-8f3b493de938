// Simple test script to verify chat functionality
// Run with: node test-chat.js

const io = require('socket.io-client');

// Replace with actual JWT tokens from your users
const USER1_TOKEN = 'your_jwt_token_here';
const USER2_TOKEN = 'another_jwt_token_here';

// Connect two users
const user1Socket = io('http://localhost:5001', {
  auth: { token: USER1_TOKEN }
});

const user2Socket = io('http://localhost:5001', {
  auth: { token: USER2_TOKEN }
});

// User 1 connection
user1Socket.on('connect', () => {
  console.log('User 1 connected');
  
  // Join a chat room
  user1Socket.emit('join-chat', 'your_chat_id_here');
});

// User 2 connection
user2Socket.on('connect', () => {
  console.log('User 2 connected');
  
  // Join the same chat room
  user2Socket.emit('join-chat', 'your_chat_id_here');
});

// Listen for messages on both users
user1Socket.on('message-received', (message) => {
  console.log('User 1 received message:', message);
});

user2Socket.on('message-received', (message) => {
  console.log('User 2 received message:', message);
});

// Listen for online status changes
user1Socket.on('user-status-changed', (data) => {
  console.log('User 1 - Status changed:', data);
});

user2Socket.on('user-status-changed', (data) => {
  console.log('User 2 - Status changed:', data);
});

// Send a test message after 2 seconds
setTimeout(() => {
  user1Socket.emit('send-message', {
    chatId: 'your_chat_id_here',
    message: 'Hello from User 1!',
    messageType: 'text'
  });
}, 2000);

// Send another message after 4 seconds
setTimeout(() => {
  user2Socket.emit('send-message', {
    chatId: 'your_chat_id_here',
    message: 'Hello back from User 2!',
    messageType: 'text'
  });
}, 4000);

// Handle errors
user1Socket.on('error', (error) => {
  console.error('User 1 error:', error);
});

user2Socket.on('error', (error) => {
  console.error('User 2 error:', error);
});

// Disconnect after 10 seconds
setTimeout(() => {
  console.log('Disconnecting...');
  user1Socket.disconnect();
  user2Socket.disconnect();
  process.exit(0);
}, 10000);
