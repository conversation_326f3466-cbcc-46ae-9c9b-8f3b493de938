import { Document } from 'mongoose';

export interface IFollowerItem {
  userId: string;
  isFollower: boolean;
}

export interface IFollowingItem {
  userId: string;
  isFollowing: boolean;
}

export interface IFollow extends Document {
  userId: string;
  followers: IFollowerItem[];
  following: IFollowingItem[];
  followersCount: number;
  followingCount: number;
  createdAt: Date;
  updatedAt: Date;
}
