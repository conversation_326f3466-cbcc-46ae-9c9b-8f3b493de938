import { Router } from 'express';
import {
  getUserFeed,
  getFeedStats,
  refreshFeed
} from '../controllers/feed.controller';
import { authenticateToken } from '../middlewares/index';

const router = Router();

// Apply JWT authentication to all feed routes
router.use(authenticateToken);

/**
 * GET /api/feed
 * Get personalized feed for the authenticated user
 * Query parameters:
 * - page: Page number (default: 1)
 * - limit: Posts per page (default: 10, max: 50)
 * 
 * Returns posts from following and followers, sorted by creation date (newest first)
 * Filters posts older than 5 days
 */
router.get('/', getUserFeed);

/**
 * GET /api/feed/refresh
 * Refresh feed - same as getUserFeed but always returns first page
 * Useful for pull-to-refresh functionality
 * Query parameters:
 * - limit: Posts per page (default: 10, max: 50)
 */
router.get('/refresh', refreshFeed);

/**
 * GET /api/feed/stats
 * Get feed statistics for the authenticated user
 * Returns information about available posts, following/followers count
 */
router.get('/stats', getFeedStats);

export default router;
