import { IHabit } from '../interfaces/habits.interface';

/**
 * Calculate collective habit completion percentage based on all habits' today todoList status
 * @param habits - Array of all user habits
 * @returns Completion percentage (0-100) based on how many habits are completed today
 */
export const calculateCollectiveHabitCompletionPercentage = (habits: IHabit[]): number => {
  if (!habits || habits.length === 0) {
    return 0;
  }

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  let completedHabitsToday = 0;
  let totalHabits = habits.length;

  // Check each habit's today status
  habits.forEach(habit => {
    const todayTodo = habit.todoList.find(item => {
      const itemDate = new Date(item.date);
      itemDate.setHours(0, 0, 0, 0);
      return itemDate.getTime() === today.getTime();
    });

    // If today's task is completed, increment counter
    if (todayTodo && todayTodo.isCompleted) {
      completedHabitsToday++;
    }
  });

  // Calculate percentage: (completed habits / total habits) * 100
  return Math.round((completedHabitsToday / totalHabits) * 100);
};
