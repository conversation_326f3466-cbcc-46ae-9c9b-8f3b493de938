import { Request, Response } from 'express';
import User from '../models/User';  // Assuming the User model is in 'models/User'

export const getAllUsers = async (req: Request, res: Response): Promise<void> => {
  try {
    const users = await User.find({}, 'userId username email');

    // Check if no users are found
    if (users.length === 0) {
      res.status(404).json({
        message: 'No users found'
      });
      return;
    }

    // Send the response with the list of users
    res.status(200).json({
      message: 'Users retrieved successfully',
      users
    });
  } catch (error) {
    console.error('Error retrieving users:', error);
    res.status(500).json({
      message: 'Failed to retrieve users'
    });
  }
};