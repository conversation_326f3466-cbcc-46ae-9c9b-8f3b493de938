import { Router } from 'express';
import { createProfile, updateProfile, getProfile, getUserProfile, changePassword } from '../controllers/profile.controller';
import { authenticateToken } from '../middlewares/index';
import { validateProfilePhoto } from '../utils/validateProfilePhoto';

const router = Router();

router.use(authenticateToken);
router.post('/create-profile', validateProfilePhoto, createProfile);
router.patch('/update-profile', validateProfilePhoto, updateProfile);
router.get('/get-profile', getProfile);
router.get('/user/:userId', getUserProfile);
router.put('/change-password', changePassword);

export default router;