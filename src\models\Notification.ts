import mongoose, { Schema, Document } from 'mongoose';

// Notification schema as separate collection
const NotificationSchema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  text: {
    type: String,
    required: true
  },
}, { timestamps: true });

// Notification interface
export interface INotification extends Document {
  userId: mongoose.Types.ObjectId;
  text: string;
  createdAt: Date;
  updatedAt: Date;
}

const Notification = mongoose.model<INotification>('Notification', NotificationSchema);

export default Notification;
