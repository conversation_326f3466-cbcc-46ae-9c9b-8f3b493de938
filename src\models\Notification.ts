import mongoose, { Schema, Document } from 'mongoose';

// Notification item schema for individual notifications
const notificationItemSchema = new Schema({
  notificationId: {
    type: mongoose.Schema.Types.ObjectId,
    default: () => new mongoose.Types.ObjectId(),
    required: true
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500
  },
  type: {
    type: String,
    required: true,
    enum: [
      'signup',
      'task_completed',
      'habit_completed', 
      'task_ended',
      'password_changed',
      'user_followed',
      'general'
    ]
  },
  isRead: {
    type: Boolean,
    default: false
  },
  date: {
    type: Date,
    default: Date.now
  },
  time: {
    type: String,
    default: () => {
      const now = new Date();
      return now.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
    }
  },
  // Additional data for specific notification types
  relatedData: {
    taskId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Task',
      required: false
    },
    habitId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Habit', 
      required: false
    },
    fromUserId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false
    },
    fromUsername: {
      type: String,
      required: false,
      trim: true
    }
  }
}, { _id: false });

// Main notification schema - stores notifications as an array in User model
export interface INotificationItem {
  notificationId: mongoose.Types.ObjectId;
  message: string;
  type: 'signup' | 'task_completed' | 'habit_completed' | 'task_ended' | 'password_changed' | 'user_followed' | 'general';
  isRead: boolean;
  date: Date;
  time: string;
  relatedData?: {
    taskId?: mongoose.Types.ObjectId;
    habitId?: mongoose.Types.ObjectId;
    fromUserId?: mongoose.Types.ObjectId;
    fromUsername?: string;
  };
}

export { notificationItemSchema };
