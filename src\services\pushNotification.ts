import admin from 'firebase-admin';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Firebase Admin SDK
const initializeFirebase = () => {
  try {
    if (!admin.apps.length) {
      const serviceKey = process.env.FCM_SERVICE_KEY;

      if (!serviceKey) {
        console.warn('FCM_SERVICE_KEY not found in environment variables. Push notifications will be disabled.');
        return false;
      }

      // Try to parse the service key (assuming it's a JSON string)
      let serviceAccount;
      try {
        serviceAccount = JSON.parse(serviceKey);
      } catch (parseError) {
        console.warn('FCM_SERVICE_KEY is not valid JSON. Push notifications will be disabled.');
        return false;
      }

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });

      console.log('Firebase Admin SDK initialized successfully');
      return true;
    }
    return true;
  } catch (error) {
    console.warn('Error initializing Firebase Admin SDK. Push notifications will be disabled:', error);
    return false;
  }
};

// Initialize Firebase on module load
const isFirebaseInitialized = initializeFirebase();

// Simple push notification function
export function sendPushNotification(deviceToken: string, title: string, message: string, sound?: string): void {
  if (!isFirebaseInitialized) {
    console.log('Firebase not initialized. Skipping push notification.');
    return;
  }

  if (!deviceToken) {
    console.error('Device token is required for push notification');
    return;
  }

  const payload: any = {
    notification: {
      title,
      body: message,
    },
    token: deviceToken,
  };

  if (sound) {
    payload.notification.sound = sound;
  }

  admin
    .messaging()
    .send(payload)
    .then((response) => {
      console.log('Push notification sent successfully:', response);
    })
    .catch((error) => {
      console.error('Error sending push notification:', error);
    });
}

// Push notification with data payload
export function sendPushNotificationData(deviceToken: string, alldata: {
  data: any;
  time: string;
  title: string;
  description: string;
  sound: string;
  androidChanel: string;
}): void {
  if (!isFirebaseInitialized) {
    console.log('Firebase not initialized. Skipping push notification with data.');
    return;
  }

  if (!deviceToken) {
    console.error('Device token is required for push notification');
    return;
  }

  const payload = {
    data: {
      data: String(alldata.data),
      time: String(alldata.time),
      title: String(alldata.title),
    },
    notification: {
      title: String(alldata.title),
      body: String(alldata.description),
      sound: String(alldata.sound),
      android_channel_id: String(alldata.androidChanel),
    },
    token: deviceToken, // Fixed: should be token, not deviceToken as first parameter
  };

  admin
    .messaging()
    .send(payload)
    .then((response) => {
      console.log('Push notification with data sent successfully:', response);
    })
    .catch((error) => {
      console.error('Error sending push notification with data:', error);
    });
}

// Helper function to send notification to user if they have notifications enabled
export async function sendNotificationToUser(
  userId: string, 
  title: string, 
  message: string, 
  sound: string = 'default'
): Promise<void> {
  try {
    // Import User model here to avoid circular dependency
    const User = (await import('../models/User')).default;
    
    const user = await User.findById(userId).select('allowNotification deviceToken');
    
    if (!user) {
      console.error('User not found for push notification:', userId);
      return;
    }

    if (!user.allowNotification) {
      console.log('User has disabled notifications:', userId);
      return;
    }

    if (!user.deviceToken) {
      console.log('User has no device token for push notification:', userId);
      return;
    }

    sendPushNotification(user.deviceToken, title, message, sound);
  } catch (error) {
    console.error('Error sending notification to user:', error);
  }
}

// Helper function to send data notification to user
export async function sendDataNotificationToUser(
  userId: string,
  notificationData: {
    data: any;
    time: string;
    title: string;
    description: string;
    sound: string;
    androidChanel: string;
  }
): Promise<void> {
  try {
    // Import User model here to avoid circular dependency
    const User = (await import('../models/User')).default;
    
    const user = await User.findById(userId).select('allowNotification deviceToken');
    
    if (!user) {
      console.error('User not found for push notification:', userId);
      return;
    }

    if (!user.allowNotification) {
      console.log('User has disabled notifications:', userId);
      return;
    }

    if (!user.deviceToken) {
      console.log('User has no device token for push notification:', userId);
      return;
    }

    sendPushNotificationData(user.deviceToken, notificationData);
  } catch (error) {
    console.error('Error sending data notification to user:', error);
  }
}
