import { Router } from 'express';
import {
  startChat,
  getUserChats,
  getChatMessages,
  markMessagesAsRead,
  uploadChatMedia
} from '../controllers/chat.controller';
import { authenticateToken } from '../middlewares/index';
import { validateChatMedia } from '../utils/validateChatMedia';

const router = Router();

// Apply JWT authentication to all chat routes
router.use(authenticateToken);

// Chat management routes
router.post('/start', startChat);                    // Start or get existing chat
router.get('/user-chats', getUserChats);             // Get all chats for user
router.get('/:chatId/messages', getChatMessages);    // Get messages for specific chat
router.put('/mark-read', markMessagesAsRead);        // Mark messages as read

// Media upload route
router.post('/upload-media', validateChatMedia, uploadChatMedia);

export default router;
