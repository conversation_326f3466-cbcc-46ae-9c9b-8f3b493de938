import { Document } from 'mongoose';
import { IFollowerItem, IFollowingItem } from './follow.interface';
import { IBlockedItem } from './block.interface';

export interface IUser extends Document {
  email: string;
  password: string;
  otp?: string;
  otpCreatedAt?: Date;
  // profile data
  username?: string;
  gender?: string;
  dob?: Date;
  about?: string;
  profilePhoto?: string;
  // Habit data
  habitCompletionPercentage: number;
  // Follow data
  followers: IFollowerItem[];
  following: IFollowingItem[];
  followersCount: number;
  followingCount: number;
  // Chat/Online status
  isOnline: boolean;
  // Block data
  blockedUsers: IBlockedItem[];
  blockedUsersCount: number;
  // Push notification settings
  allowNotification: boolean;
  deviceToken?: string;
}