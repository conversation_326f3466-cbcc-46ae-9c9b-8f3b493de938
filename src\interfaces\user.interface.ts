import { Document } from 'mongoose';
import { IFollowerItem, IFollowingItem } from './follow.interface';
import { IBlockedItem } from './block.interface';
import { INotificationItem } from './notification.interface';

export interface IUser extends Document {
  email: string;
  password: string;
  otp?: string;
  otpCreatedAt?: Date;
  // profile data
  username?: string;
  gender?: string;
  dob?: Date;
  about?: string;
  profilePhoto?: string;
  // Habit data
  habitCompletionPercentage: number;
  // Follow data
  followers: IFollowerItem[];
  following: IFollowingItem[];
  followersCount: number;
  followingCount: number;
  // Chat/Online status
  isOnline: boolean;
  // Block data
  blockedUsers: IBlockedItem[];
  blockedUsersCount: number;
  // Notification data
  notifications: INotificationItem[];
}