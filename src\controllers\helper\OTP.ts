import type { Request, Response } from 'express';
import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { StatusCodes } from 'http-status-codes';
import User from '../../models/User';
dotenv.config();

export const sendOtpEmail = async (email: string, otp: string) => {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER, 
      pass: process.env.EMAIL_PASS
    }
  });

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: 'Password Reset OTP',
    text: `Your OTP for password reset is: ${otp}`
  };

  await transporter.sendMail(mailOptions);
};

// Generate OTP and store it in the user's document (when user click on reset password it will generate OTP and send it to user's email)
export const resetPasswordGenerateOTP = async (req: Request, res: Response) => {
  const { email } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    // Generate 4-digit OTP
    const otp = Math.floor(1000 + Math.random() * 9000).toString();

    // Send OTP to user's email
    await sendOtpEmail(email, otp);

    // Set OTP value to null before storing it (ensures old value is cleared)
    user['otp'] = "";  
    user['otpCreatedAt'] = new Date();   // Store the current time as OTP generation time
    user['otp'] = otp;   // Store the new OTP
    await user.save();

    return res.status(StatusCodes.OK).json({ message: 'OTP sent to your email' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error sending OTP', error });
  }
};


// Verify OTP function
export const verifyOtp = async (req: Request, res: Response) => {
  const { email, otp } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    if (!user['otp']) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP has not been generated. Click Resend to generate a new one.' });
    }

    // Check if OTP is expired ... 2 minutes expiration time
    console.log("OTP Created At:", user['otpCreatedAt']);
    const otpCreatedAt = user['otpCreatedAt'] instanceof Date ? user['otpCreatedAt'].getTime() : 0;
    const otpAge = new Date().getTime() - otpCreatedAt;
    console.log("OTP Age in milliseconds:", otpAge); 
    const otpExpirationTime = 2 * 60 * 1000; // 2 minutes in milliseconds
    if (otpAge > otpExpirationTime) {
      user['otp'] = "";  
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP has expired. Please request a new OTP' });
    }

    if (user['otp'] !== otp) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP does not match' });
    }

    user['otp'] = "";
    await user.save();

    return res.status(StatusCodes.OK).json({ message: 'OTP verified' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error verifying OTP', error });
  }
};

