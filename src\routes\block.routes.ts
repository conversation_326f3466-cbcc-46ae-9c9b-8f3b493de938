import { Router } from 'express';
import {
  blockUser,
  unblockUser,
  getBlockedUsers,
  checkBlockStatus
} from '../controllers/block.controller';
import { authenticateToken } from '../middlewares/auth';

const router = Router();

// Apply JWT authentication to all block routes
router.use(authenticateToken);

// Block operations
router.post('/block', blockUser);           // Block a user
router.post('/unblock', unblockUser);       // Unblock a user

// Block information
router.get('/blocked-users', getBlockedUsers);        // Get list of blocked users
router.get('/status/:userId', checkBlockStatus);      // Check block status with specific user

export default router;
