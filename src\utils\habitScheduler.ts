import cron from 'node-cron';
import Habit from '../models/Habit';
import { getTodayDeviceDate, formatLocalDate, isSameLocalDay } from './dateUtils';

// Function to add daily entries to all habits based on device date
export const addDailyHabitEntries = async (): Promise<void> => {
  try {
    console.log('Running daily habit entries update based on device date...');

    // Get today's date based on device time, not UTC
    const today = getTodayDeviceDate();

    const habits = await Habit.find({});
    let updatedCount = 0;
    let alreadyExistsCount = 0;

    for (const habit of habits) {
      // Check if today's date already exists in todoList
      const todayExists = habit.todoList.some(item => {
        return isSameLocalDay(new Date(item.date), today);
      });

      if (!todayExists) {
        habit.todoList.push({
          date: today,
          isCompleted: false
        });
        await habit.save();
        updatedCount++;
        console.log(`Added today's date (${formatLocalDate(today)}) to habit: ${habit.title}`);
      } else {
        alreadyExistsCount++;
        console.log(`Today's date (${formatLocalDate(today)}) already exists in habit: ${habit.title}`);
      }
    }

    console.log(`Daily habit entries update completed for ${formatLocalDate(today)}:`);
    console.log(`- Updated habits: ${updatedCount}`);
    console.log(`- Habits already had today's date: ${alreadyExistsCount}`);
    console.log(`- Total habits processed: ${habits.length}`);
  } catch (error) {
    console.error('Error adding daily habit entries:', error);
  }
};

// Schedule the daily task to run at midnight every day based on local time
export const startHabitScheduler = (): void => {
  // Run at 00:01 every day (1 minute after midnight) in local timezone
  cron.schedule('1 0 * * *', addDailyHabitEntries);

  console.log('Habit scheduler started - will run daily at 00:01 local time');
};

// Manual trigger for testing - can be called to test the functionality
export const triggerDailyUpdate = addDailyHabitEntries;

// Function to manually add today's date to all habits (for testing purposes)
export const manuallyAddTodaysDate = async (): Promise<void> => {
  console.log('Manually triggering daily habit entries update...');
  await addDailyHabitEntries();
};
