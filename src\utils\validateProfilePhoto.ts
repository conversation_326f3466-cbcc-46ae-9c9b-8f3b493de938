
import { Request, Response, NextFunction } from "express";
import multer, { MulterError } from "multer";


// const upload = multer({ storage: multer.memoryStorage() }).fields([
//   { name: "profilePhoto" },
// ]);
// export const validateUpload = (req: Request, res: Response, next: NextFunction) => {
//   upload(req, res, (error: any) => {
//     if (error) {
//       console.error("Multer Error:", error);
//       if (error instanceof MulterError) {
//         res.status(400).json({ message: `Multer Error: ${error.message}` });
//       } else {
//         res.status(500).json({ message: `Internal Error: ${error.message}` });
//       }

//       req.destroy();
//       return;
//     }
//     next();
//   });
// };

const updateProfilePhoto = multer({ 
  storage: multer.memoryStorage(),
  fileFilter: (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];

    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Only image files are allowed!'));
    }
    cb(null, true);
  }
}).single('profilePhoto');

export const validateProfilePhoto = (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  updateProfilePhoto(req, res, (error: any) => {
    if (error) {
      console.error("Multer Error:", error);
      
      if (error instanceof MulterError) {
        res.status(400).json({ message: `Multer Error: ${error.message}` });
      } else {
        res.status(400).json({ message: error.message });  
      }
      
      req.destroy();  // Clean up
      return;
    }
    next();
  });
};