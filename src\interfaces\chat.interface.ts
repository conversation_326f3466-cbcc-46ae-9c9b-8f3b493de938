import { Document } from 'mongoose';

export interface IChatMessage extends Document {
  chatId: string;
  senderId: string;
  receiverId: string;
  message: string;
  media?: string[]; // Array of media URLs (up to 5 images/videos)
  messageType: 'text' | 'media' | 'mixed';
  isRead: boolean;
  sentAt: Date;
  readAt?: Date;
}

export interface IChat extends Document {
  participants: string[]; // Array of user IDs
  lastMessage?: string;
  lastMessageAt?: Date;
  lastMessageSenderId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IChatParticipant {
  userId: string;
  username: string;
  email: string;
  profilePhoto: string;
  isOnline: boolean;
}

export interface IChatRoom {
  chatId: string;
  participants: IChatParticipant[];
  messages: IChatMessage[];
  unreadCount: number;
}

export interface IOnlineUser {
  userId: string;
  socketId: string;
  username?: string;
  email?: string;
  profilePhoto?: string;
  connectedAt: Date;
}

export interface IChatResponse {
  success: boolean;
  message: string;
  data?: any;
}

export interface IStartChatRequest {
  receiverUserId: string;
}

export interface IGetChatsResponse {
  chats: {
    chatId: string;
    participant: IChatParticipant;
    lastMessage?: string;
    lastMessageAt?: Date;
    unreadCount: number;
  }[];
}

export interface IGetChatMessagesResponse {
  chatId: string;
  participant: IChatParticipant;
  messages: IChatMessage[];
  hasMore: boolean;
  page: number;
  totalPages: number;
}

export interface ISendMessageRequest {
  chatId: string;
  message?: string;
  media?: string[]; // URLs of uploaded media
  messageType: 'text' | 'media' | 'mixed';
}

export interface ISocketEvents {
  // Client to Server events
  'join-chat': (chatId: string) => void;
  'leave-chat': (chatId: string) => void;
  'send-message': (data: ISendMessageRequest) => void;
  'mark-as-read': (data: { chatId: string; messageIds: string[] }) => void;
  'typing-start': (data: { chatId: string; receiverId: string }) => void;
  'typing-stop': (data: { chatId: string; receiverId: string }) => void;
  'user-online': () => void;
  'user-offline': () => void;

  // Server to Client events
  'message-received': (message: IChatMessage) => void;
  'message-read': (data: { chatId: string; messageIds: string[]; readBy: string }) => void;
  'user-typing': (data: { chatId: string; userId: string; username: string }) => void;
  'user-stopped-typing': (data: { chatId: string; userId: string }) => void;
  'user-status-changed': (data: { userId: string; isOnline: boolean }) => void;
  'chat-updated': (data: { chatId: string; lastMessage: string; lastMessageAt: Date }) => void;
  'error': (error: { message: string; code?: string }) => void;
}
