// import nodemailer from 'nodemailer';
// import dotenv from 'dotenv';
// dotenv.config();
// interface EmailOptions {
//   to: string;
//   subject: string;
//   text: string;
//   html?: string;
// }
// interface TransporterConfig {
//   host: string;
//   port: number;
//   secure: boolean;
//   auth: {
//     user: string;
//     pass: string;
//   };
// }
// class EmailService {
//   private transporter: any;
//   constructor() {
//     const config: TransporterConfig = {
//       host: process.env.EMAIL_HOST || 'smtp.gmail.com',
//       port: parseInt(process.env.EMAIL_PORT || '587'),
//       secure: false,
//       auth: {
//         user: process.env.EMAIL_USER || '',
//         pass: process.env.EMAIL_PASS || '',
//       },
//     };
//     this.transporter = nodemailer.createTransport(config);
//   }
//   async sendEmail(options: EmailOptions): Promise<void> {
//     try {
//       const mailOptions = {
//         from: `<PERSON><PERSON>els <${process.env.EMAIL_USER}>`,
//         to: options.to,
//         subject: options.subject,
//         text: options.text,
//         html: options.html,
//       };
//       await this.transporter.sendMail(mailOptions);
//       console.log(`Email sent to ${options.to}`);
//     } catch (error) {
//       console.error('Email sending error:', error);
//       throw new Error('Failed to send email');
//     }
//   }
//   async sendPasswordResetCode(email: string, code: string): Promise<void> {
//     const subject = 'Cinder Reels Password Reset Code';
//     const text = `Your password reset code is: ${code}\n\nThis code will expire in 5 minutes.`;
//     await this.sendEmail({
//       to: email,
//       subject,
//       text,
//     });
//   }
//   async sendWelcomeEmail(email: string, name: string): Promise<void> {
//     const subject = 'Welcome to Cinder Reels';
//     const text = `Welcome ${name}! Thank you for joining Cinder Reels.`;
//     await this.sendEmail({
//       to: email,
//       subject,
//       text,
//     });
//   }
// }
// export default new EmailService();






