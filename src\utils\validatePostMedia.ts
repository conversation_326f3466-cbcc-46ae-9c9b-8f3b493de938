import { Request, Response, NextFunction } from "express";
import multer, { MulterError } from "multer";

// Configure multer for multiple file uploads (images and videos)
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit per file
    files: 10 // Maximum 10 files per upload
  },
  fileFilter: (req, file, cb) => {
    // Accept images and videos
    if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image and video files are allowed'));
    }
  }
}).array('media', 10); // Accept up to 10 files with field name 'media'

export const validatePostMedia = (req: Request, res: Response, next: NextFunction) => {
  upload(req, res, (error: any) => {
    if (error) {
      console.error("Multer Error:", error);
      if (error instanceof MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
          res.status(400).json({ message: 'File size too large. Maximum 50MB per file.' });
        } else if (error.code === 'LIMIT_FILE_COUNT') {
          res.status(400).json({ message: 'Too many files. Maximum 10 files allowed.' });
        } else {
          res.status(400).json({ message: `Upload Error: ${error.message}` });
        }
      } else {
        res.status(400).json({ message: `Upload Error: ${error.message}` });
      }
      req.destroy();
      return;
    }
    next();
  });
};
