import { Router } from 'express';
import {
  followUser,
  unfollowUser,
  getFollowers,
  getFollowing,
  removeFollower
} from '../controllers/follow.controller';
import { authenticateToken } from '../middlewares/auth';

const router = Router();

router.use(authenticateToken);

router.post('/follow', followUser);
router.post('/unfollow', unfollowUser);
router.post('/remove-follower', removeFollower);

router.get('/followers', getFollowers);
router.get('/following', getFollowing);

export default router;
