/**
 * Utility functions for date handling
 */

/**
 * Gets today's date based on device/local time (not UTC)
 * Returns a Date object set to the start of the current day in local time
 */
export const getTodayDeviceDate = (): Date => {
  const now = new Date();
  // Create a new date using local time components to avoid UTC conversion
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  return today;
};

/**
 * Compares two dates to check if they represent the same day in local time
 * Ignores time components and only compares year, month, and day
 */
export const isSameLocalDay = (date1: Date, date2: Date): boolean => {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  
  return (
    d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate()
  );
};

/**
 * Formats a date to a readable string in local time
 */
export const formatLocalDate = (date: Date): string => {
  return date.toDateString();
};
