

import Habit from '../../models/Habit';
import User from '../../models/User';
import { calculateCollectiveHabitCompletionPercentage } from '../../utils/habitCompletionPercentage';

// Helper function to update user's collective habit completion percentage
export const updateUserHabitCompletionPercentage = async (userId: string): Promise<void> => {
  // Get all habits for the user
  const habits = await Habit.find({ userId });

  // Calculate collective completion percentage
  const completionPercentage = calculateCollectiveHabitCompletionPercentage(habits);
  console.log('Completion percentage:', completionPercentage);

  // Update user's completion percentage
  await User.findByIdAndUpdate(userId, { habitCompletionPercentage: completionPercentage });
};
