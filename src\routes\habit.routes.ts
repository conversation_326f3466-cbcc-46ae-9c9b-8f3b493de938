import { Router } from 'express';
import {
  createHabit,
  getAllHabits,
  getAllHabitTitles,
  updateHabit,
  deleteHabit
} from '../controllers/habit.controller';
import { authenticateToken } from '../middlewares/index';
import { updateUserHabitCompletionPercentage } from '../controllers/helper/habits';
import { manuallyAddTodaysDate } from '../utils/habitScheduler';

const router = Router();

router.use(authenticateToken);

router.post('/', createHabit);
router.get('/', getAllHabits);
router.get('/titles', getAllHabitTitles);
router.put('/:habitId', updateHabit);
router.delete('/:habitId', deleteHabit);

// Test route to manually trigger daily habit update (for testing purposes)
router.post('/trigger-daily-update', async (_req, res) => {
  try {
    await manuallyAddTodaysDate();
    res.status(200).json({
      message: 'Daily habit update triggered successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error triggering daily habit update:', error);
    res.status(500).json({
      message: 'Error triggering daily habit update',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// when user mark the todays habit as true then if the percentage is not updating then call router.put('/:habitId', updateHabit); but for now its updating.
export default router;
