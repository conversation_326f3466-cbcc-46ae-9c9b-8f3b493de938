import { Response } from 'express';
import Community from '../models/Community';
import Post from '../models/Post';
import User from '../models/User';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middlewares/auth';
import { uploadFile, deleteFile } from '../utils/mediaHandling';

// Create a new community
export const createCommunity = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { title, description } = req.body;
    const userId = req.user?.userId;
    const file = req.file as Express.Multer.File;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate required fields
    if (!title || title.trim() === '') {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Community title is required'
      });
      return;
    }

    if (!description || description.trim() === '') {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Community description is required'
      });
      return;
    }

    // Get user details for author info
    const user = await User.findById(userId);
    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    let backgroundImageUrl = "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80";

    // Upload background image if provided
    if (file) {
      try {
        backgroundImageUrl = await uploadFile(file);
      } catch (uploadError) {
        console.error('Error uploading background image:', uploadError);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          message: 'Failed to upload background image'
        });
        return;
      }
    }

    const newCommunity = new Community({
      title: title.trim(),
      description: description.trim(),
      backgroundImage: backgroundImageUrl,
      author: userId,
      members: [{
        userId: userId,
        username: user.username || 'Unknown',
        profilePhoto: user.profilePhoto || '',
        joinedAt: new Date()
      }],
      membersCount: 1
    });

    const savedCommunity = await newCommunity.save();
    await savedCommunity.populate('author', 'username profilePhoto');

    res.status(StatusCodes.CREATED).json({
      message: 'Community created successfully',
      community: savedCommunity
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error creating community:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error creating community',
        error: error.message,
      });
    }
  }
};

// Update community (only author can update)
export const updateCommunity = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { communityId } = req.params;
    const { title, description } = req.body;
    const userId = req.user?.userId;
    const file = req.file as Express.Multer.File;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const community = await Community.findById(communityId);
    if (!community) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Community not found'
      });
      return;
    }

    // Check if user is the author
    if (community.author.toString() !== userId) {
      res.status(StatusCodes.FORBIDDEN).json({
        message: 'Only the community author can update the community'
      });
      return;
    }

    // Update fields if provided
    if (title !== undefined && title.trim() !== '') {
      community.title = title.trim();
    }
    if (description !== undefined && description.trim() !== '') {
      community.description = description.trim();
    }

    // Update background image if provided
    if (file) {
      try {
        const newBackgroundImage = await uploadFile(file);
        // Delete old background image if it's not the default
        if (community.backgroundImage && !community.backgroundImage.includes('unsplash.com')) {
          await deleteFile(community.backgroundImage);
        }
        community.backgroundImage = newBackgroundImage;
      } catch (uploadError) {
        console.error('Error uploading new background image:', uploadError);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          message: 'Failed to upload new background image'
        });
        return;
      }
    }

    const updatedCommunity = await community.save();
    await updatedCommunity.populate('author', 'username profilePhoto');

    res.status(StatusCodes.OK).json({
      message: 'Community updated successfully',
      community: updatedCommunity
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error updating community:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error updating community',
        error: error.message,
      });
    }
  }
};

// Delete community (only author can delete, cascades to posts and members)
export const deleteCommunity = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { communityId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const community = await Community.findById(communityId);
    if (!community) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Community not found'
      });
      return;
    }

    // Check if user is the author
    if (community.author.toString() !== userId) {
      res.status(StatusCodes.FORBIDDEN).json({
        message: 'Only the community author can delete the community'
      });
      return;
    }

    // Delete all posts in this community
    const communityPosts = await Post.find({ communityId: communityId });
    for (const post of communityPosts) {
      // Delete media files from S3
      if (post.media && post.media.length > 0) {
        for (const mediaUrl of post.media) {
          try {
            await deleteFile(mediaUrl);
          } catch (deleteError) {
            console.error('Error deleting media file:', deleteError);
          }
        }
      }
    }

    // Delete all posts in the community
    await Post.deleteMany({ communityId: communityId });

    // Delete background image if it's not the default
    if (community.backgroundImage && !community.backgroundImage.includes('unsplash.com')) {
      try {
        await deleteFile(community.backgroundImage);
      } catch (deleteError) {
        console.error('Error deleting background image:', deleteError);
      }
    }

    // Delete the community
    await Community.findByIdAndDelete(communityId);

    res.status(StatusCodes.OK).json({
      message: 'Community and all associated data deleted successfully'
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error deleting community:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error deleting community',
        error: error.message,
      });
    }
  }
};

// Get all communities
export const getAllCommunities = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const communities = await Community.find()
      .populate('author', 'username profilePhoto')
      .sort({ createdAt: -1 });

    // Add isJoined status for each community
    const communitiesWithJoinStatus = communities.map(community => {
      const isJoined = community.members.some(member => member.userId.toString() === userId);
      return {
        ...community.toObject(),
        isJoined
      };
    });

    res.status(StatusCodes.OK).json({
      message: 'Communities retrieved successfully',
      communities: communitiesWithJoinStatus
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error getting communities:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error retrieving communities',
        error: error.message,
      });
    }
  }
};

// Join a community
export const joinCommunity = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { communityId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const community = await Community.findById(communityId);
    if (!community) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Community not found'
      });
      return;
    }

    // Check if user is already a member
    const isAlreadyMember = community.members.some(member => member.userId.toString() === userId);
    if (isAlreadyMember) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'You are already a member of this community'
      });
      return;
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'User not found'
      });
      return;
    }

    // Add user to community members
    community.members.push({
      userId: userId,
      username: user.username || 'Unknown',
      profilePhoto: user.profilePhoto || '',
      joinedAt: new Date()
    });
    community.membersCount = community.members.length;

    await community.save();

    res.status(StatusCodes.OK).json({
      message: 'Successfully joined the community',
      community: {
        _id: community._id,
        title: community.title,
        membersCount: community.membersCount,
        isJoined: true
      }
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error joining community:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error joining community',
        error: error.message,
      });
    }
  }
};

// Leave a community
export const leaveCommunity = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { communityId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const community = await Community.findById(communityId);
    if (!community) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Community not found'
      });
      return;
    }

    // Check if user is a member
    const memberIndex = community.members.findIndex(member => member.userId.toString() === userId);
    if (memberIndex === -1) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'You are not a member of this community'
      });
      return;
    }

    // Check if user is the author (author cannot leave their own community)
    if (community.author.toString() === userId) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Community author cannot leave their own community. Delete the community instead.'
      });
      return;
    }

    // Remove user from community members
    community.members.splice(memberIndex, 1);
    community.membersCount = community.members.length;

    await community.save();

    res.status(StatusCodes.OK).json({
      message: 'Successfully left the community',
      community: {
        _id: community._id,
        title: community.title,
        membersCount: community.membersCount,
        isJoined: false
      }
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error leaving community:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error leaving community',
        error: error.message,
      });
    }
  }
};

// Get communities created by a user
export const getUserCreatedCommunities = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const communities = await Community.find({ author: userId })
      .populate('author', 'username profilePhoto')
      .sort({ createdAt: -1 });

    res.status(StatusCodes.OK).json({
      message: 'User created communities retrieved successfully',
      communities
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error getting user created communities:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error retrieving user created communities',
        error: error.message,
      });
    }
  }
};

// Get communities joined by a user
export const getUserJoinedCommunities = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const communities = await Community.find({ 'members.userId': userId })
      .populate('author', 'username profilePhoto')
      .select('-members')
      .sort({ createdAt: -1 });

    // Add isJoined status (will always be true for this endpoint)
    const communitiesWithJoinStatus = communities.map(community => ({
      ...community.toObject(),
      isJoined: true
    }));

    res.status(StatusCodes.OK).json({
      message: 'User joined communities retrieved successfully',
      communities: communitiesWithJoinStatus
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error getting user joined communities:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error retrieving user joined communities',
        error: error.message,
      });
    }
  }
};
export const getTopCommunities = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const communities = await Community.find()
      .populate('author', 'username profilePhoto') // Populate the author details
      .select('-members') // Exclude the 'members' field
      .sort({ membersCount: -1 }) // Sort communities by membersCount
      .limit(10);

    // Add isJoined status for each community
    const communitiesWithJoinStatus = communities.map(community => {
      // Ensure 'members' field exists before accessing it
      const isJoined = community.members && community.members.some(member => member.userId.toString() === userId);
      return {
        ...community.toObject(),
        isJoined
      };
    });

    res.status(StatusCodes.OK).json({
      message: 'Top 10 communities retrieved successfully',
      communities: communitiesWithJoinStatus
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error getting top communities:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error retrieving top communities',
        error: error.message,
      });
    }
  }
};

// Get community member count
export const getCommunityMemberCount = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { communityId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const community = await Community.findById(communityId);
    if (!community) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Community not found'
      });
      return;
    }

    res.status(StatusCodes.OK).json({
      message: 'Community member count retrieved successfully',
      communityId: community._id,
      title: community.title,
      membersCount: community.membersCount
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error getting community member count:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error retrieving community member count',
        error: error.message,
      });
    }
  }
};

// Get all posts of a community
export const getCommunityPosts = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { communityId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Check if community exists
    const community = await Community.findById(communityId);
    if (!community) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Community not found'
      });
      return;
    }

    // Get all posts for this community
    const posts = await Post.find({ communityId: communityId })
      .populate('userId', 'username profilePhoto')
      .populate('communityId', 'title')
      .sort({ createdAt: -1 });

    res.status(StatusCodes.OK).json({
      message: 'Community posts retrieved successfully',
      community: {
        _id: community._id,
        title: community.title,
        membersCount: community.membersCount
      },
      posts
    });

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error getting community posts:', error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        message: 'Error retrieving community posts',
        error: error.message,
      });
    }
  }
};
