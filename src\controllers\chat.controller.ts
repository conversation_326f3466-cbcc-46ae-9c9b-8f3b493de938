import type { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import Chat from '../models/Chat';
import ChatMessage from '../models/ChatMessage';
import User from '../models/User';
import { AuthenticatedRequest } from '../middlewares/auth';
import {
  IStartChatRequest,
  IChatResponse,
  IGetChatsResponse,
  IGetChatMessagesResponse,
  ISendMessageRequest
} from '../interfaces/index';
import { uploadFile } from '../utils/mediaHandling';

// Start or get existing chat with a user
export const startChat = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    const { receiverUserId }: IStartChatRequest = req.body;
 
    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    if (!receiverUserId) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Receiver user ID is required'
      });
      return;
    }

    if (userId === receiverUserId) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Cannot start chat with yourself'
      });
      return;
    }

    // Check if receiver user exists and get current user for block checks
    const [receiverUser, currentUser] = await Promise.all([
      User.findById(receiverUserId).select('username email profilePhoto isOnline blockedUsers'),
      User.findById(userId).select('blockedUsers')
    ]);

    if (!receiverUser || !currentUser) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Check if current user has blocked the receiver
    const hasBlockedReceiver = currentUser.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === receiverUserId && blockedItem.isBlocked
    );

    if (hasBlockedReceiver) {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Cannot start chat with a user you have blocked'
      });
      return;
    }

    // Check if current user is blocked by the receiver
    const isBlockedByReceiver = receiverUser.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === userId && blockedItem.isBlocked
    );

    if (isBlockedByReceiver) {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Cannot start chat as you are blocked by this user'
      });
      return;
    }

    // Check if chat already exists between these users
    let existingChat = await Chat.findOne({
      participants: { $all: [userId, receiverUserId] }
    });

    if (existingChat) {
      res.status(StatusCodes.OK).json({
        success: true,
        message: 'Chat retrieved successfully',
        data: {
          chatId: existingChat._id,
          participant: {
            userId: receiverUser._id,
            username: receiverUser.username,
            email: receiverUser.email,
            profilePhoto: receiverUser.profilePhoto,
            isOnline: receiverUser.isOnline
          }
        }
      });
      return;
    }

    // Create new chat
    const newChat = new Chat({
      participants: [userId, receiverUserId]
    });

    await newChat.save();

    res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Chat created successfully',
      data: {
        chatId: newChat._id,
        participant: {
          userId: receiverUser._id,
          username: receiverUser.username,
          email: receiverUser.email,
          profilePhoto: receiverUser.profilePhoto,
          isOnline: receiverUser.isOnline
        }
      }
    });

  } catch (error) {
    console.error('Error in startChat:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get all chats for a user
export const getUserChats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    // Get current user's blocked users list
    const currentUser = await User.findById(userId).select('blockedUsers');
    if (!currentUser) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Get all chats where user is a participant
    const chats = await Chat.find({
      participants: userId
    })
    .populate('participants', 'username email profilePhoto isOnline blockedUsers')
    .sort({ lastMessageAt: -1 });

    const chatList = await Promise.all(
      chats.map(async (chat) => {
        // Get the other participant (not the current user)
        const otherParticipant = chat.participants.find(
          (participant: any) => participant._id.toString() !== userId
        ) as any;

        if (!otherParticipant) {
          return null; // Skip this chat if no other participant found
        }

        // Check if current user has blocked the other participant
        const hasBlockedOther = currentUser.blockedUsers.some(
          (blockedItem) => blockedItem.userId.toString() === otherParticipant._id.toString() && blockedItem.isBlocked
        );

        // Check if current user is blocked by the other participant
        const isBlockedByOther = otherParticipant.blockedUsers?.some(
          (blockedItem: any) => blockedItem.userId.toString() === userId && blockedItem.isBlocked
        );

        // Skip this chat if there's any blocking relationship
        if (hasBlockedOther || isBlockedByOther) {
          return null;
        }

        // Get unread message count for this chat
        const unreadCount = await ChatMessage.countDocuments({
          chatId: chat._id,
          receiverId: userId,
          isRead: false
        });

        return {
          chatId: (chat._id as any).toString(),
          participant: {
            userId: otherParticipant._id.toString(),
            username: otherParticipant.username || '',
            email: otherParticipant.email || '',
            profilePhoto: otherParticipant.profilePhoto || '',
            isOnline: otherParticipant.isOnline || false
          },
          lastMessage: chat.lastMessage,
          lastMessageAt: chat.lastMessageAt,
          unreadCount
        };
      })
    ).then(results => results.filter(Boolean)) as any; // Filter out null results

    const response: IGetChatsResponse = {
      chats: chatList
    };

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Chats retrieved successfully',
      data: response
    });

  } catch (error) {
    console.error('Error in getUserChats:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get messages for a specific chat
export const getChatMessages = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    const { chatId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    // Verify user is participant in this chat and get current user for block checks
    const [chat, currentUser] = await Promise.all([
      Chat.findOne({
        _id: chatId,
        participants: userId
      }).populate('participants', 'username email profilePhoto isOnline blockedUsers'),
      User.findById(userId).select('blockedUsers')
    ]);

    if (!chat || !currentUser) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Chat not found or access denied'
      });
      return;
    }

    // Get the other participant
    const otherParticipant = chat.participants.find(
      (participant: any) => participant._id.toString() !== userId
    ) as any;

    if (!otherParticipant) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Other participant not found'
      });
      return;
    }

    // Check if current user has blocked the other participant
    const hasBlockedOther = currentUser.blockedUsers.some(
      (blockedItem) => blockedItem.userId.toString() === otherParticipant._id.toString() && blockedItem.isBlocked
    );

    // Check if current user is blocked by the other participant
    const isBlockedByOther = otherParticipant.blockedUsers?.some(
      (blockedItem: any) => blockedItem.userId.toString() === userId && blockedItem.isBlocked
    );

    // Deny access if there's any blocking relationship
    if (hasBlockedOther || isBlockedByOther) {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Access denied due to blocking relationship'
      });
      return;
    }

    // Get messages with pagination
    const skip = (page - 1) * limit;
    const messages = await ChatMessage.find({ chatId })
      .sort({ sentAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('senderId', 'username profilePhoto')
      .populate('receiverId', 'username profilePhoto');

    const totalMessages = await ChatMessage.countDocuments({ chatId });
    const totalPages = Math.ceil(totalMessages / limit);

    const response: IGetChatMessagesResponse = {
      chatId,
      participant: {
        userId: otherParticipant._id.toString(),
        username: otherParticipant.username || '',
        email: otherParticipant.email || '',
        profilePhoto: otherParticipant.profilePhoto || '',
        isOnline: otherParticipant.isOnline || false
      },
      messages: messages.reverse(), // Reverse to show oldest first
      hasMore: page < totalPages,
      page,
      totalPages
    };

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Messages retrieved successfully',
      data: response
    });

  } catch (error) {
    console.error('Error in getChatMessages:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Mark messages as read
export const markMessagesAsRead = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    const { chatId, messageIds } = req.body;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    // Verify user is participant in this chat
    const chat = await Chat.findOne({
      _id: chatId,
      participants: userId
    });

    if (!chat) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Chat not found or access denied'
      });
      return;
    }

    // Mark messages as read
    await ChatMessage.updateMany(
      {
        _id: { $in: messageIds },
        receiverId: userId,
        isRead: false
      },
      {
        isRead: true,
        readAt: new Date()
      }
    );

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Messages marked as read'
    });

  } catch (error) {
    console.error('Error in markMessagesAsRead:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Upload media for chat
export const uploadChatMedia = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
      return;
    }

    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'No files uploaded'
      });
      return;
    }

    if (files.length > 5) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Maximum 5 files allowed per upload'
      });
      return;
    }

    // Upload files to S3
    const uploadPromises = files.map(file => uploadFile(file));
    const mediaUrls = await Promise.all(uploadPromises);

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Media uploaded successfully',
      data: {
        mediaUrls
      }
    });

  } catch (error) {
    console.error('Error in uploadChatMedia:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to upload media'
    });
  }
};
